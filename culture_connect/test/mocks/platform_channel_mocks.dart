import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

/// Centralized platform channel mocking system for CultureConnect tests
///
/// This class provides comprehensive mocking for all native plugin dependencies
/// without requiring modifications to production code. Tests must adapt to
/// existing service interfaces.
class PlatformChannelMocks {
  static bool _isInitialized = false;
  static final Map<String, Map<String, dynamic>> _sharedPreferencesData = {};
  static final Map<String, dynamic> _firebaseAuthState = {
    'currentUser': null,
    'isSignedIn': false,
  };

  /// Initialize all platform channel mocks
  static void initialize() {
    if (_isInitialized) return;

    // Initialize Firebase Core first (required for other Firebase services)
    _setupFirebaseCoreMocks();
    _setupFirebaseAuthMocks();
    _setupFirestoreMocks();
    _setupSharedPreferencesMocks();
    _setupLocalNotificationsMocks();
    _setupPathProviderMocks();
    _setupSharePlusMocks();
    _setupConnectivityPlusMocks();
    _setupSpeechToTextMocks();

    _isInitialized = true;
  }

  /// Clean up all platform channel mocks
  static void cleanup() {
    if (!_isInitialized) return;

    _clearFirebaseAuthMocks();
    _clearFirestoreMocks();
    _clearSharedPreferencesMocks();
    _clearLocalNotificationsMocks();
    _clearPathProviderMocks();
    _clearSharePlusMocks();
    _clearConnectivityPlusMocks();
    _clearSpeechToTextMocks();
    _clearFirebaseCoreMocks();

    _sharedPreferencesData.clear();
    _firebaseAuthState.clear();
    _firebaseAuthState['currentUser'] = null;
    _firebaseAuthState['isSignedIn'] = false;

    _isInitialized = false;
  }

  /// Setup Firebase Auth mocking
  static void _setupFirebaseAuthMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/firebase_auth'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'Auth#registerIdTokenListener':
            return {'name': 'test_listener'};
          case 'Auth#registerAuthStateListener':
            return {'name': 'test_auth_listener'};
          case 'Auth#signInWithEmailAndPassword':
            _firebaseAuthState['currentUser'] = {
              'uid': 'test_uid',
              'email': methodCall.arguments['email'],
              'displayName': 'Test User',
            };
            _firebaseAuthState['isSignedIn'] = true;
            return _firebaseAuthState['currentUser'];
          case 'Auth#createUserWithEmailAndPassword':
            _firebaseAuthState['currentUser'] = {
              'uid': 'test_uid_new',
              'email': methodCall.arguments['email'],
              'displayName': 'New Test User',
            };
            _firebaseAuthState['isSignedIn'] = true;
            return _firebaseAuthState['currentUser'];
          case 'Auth#signOut':
            _firebaseAuthState['currentUser'] = null;
            _firebaseAuthState['isSignedIn'] = false;
            return null;
          case 'Auth#currentUser':
            return _firebaseAuthState['currentUser'];
          case 'Auth#authStateChanges':
            return null;
          case 'Auth#idTokenChanges':
            return null;
          case 'Auth#userChanges':
            return null;
          default:
            return null;
        }
      },
    );
  }

  /// Setup Firestore mocking
  static void _setupFirestoreMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/cloud_firestore'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'Firestore#runTransaction':
            return {'result': 'success'};
          case 'DocumentReference#set':
            return null;
          case 'DocumentReference#update':
            return null;
          case 'DocumentReference#get':
            return {
              'data': {},
              'exists': true,
            };
          case 'Query#get':
            return {
              'documents': [],
              'metadata': {'isFromCache': false},
            };
          case 'CollectionReference#add':
            return {'path': 'mock/doc/path'};
          default:
            return null;
        }
      },
    );
  }

  /// Setup SharedPreferences mocking
  static void _setupSharedPreferencesMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/shared_preferences'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'getAll':
            return _sharedPreferencesData;
          case 'setBool':
            _sharedPreferencesData[methodCall.arguments['key']] =
                methodCall.arguments['value'];
            return true;
          case 'setInt':
            _sharedPreferencesData[methodCall.arguments['key']] =
                methodCall.arguments['value'];
            return true;
          case 'setString':
            _sharedPreferencesData[methodCall.arguments['key']] =
                methodCall.arguments['value'];
            return true;
          case 'setDouble':
            _sharedPreferencesData[methodCall.arguments['key']] =
                methodCall.arguments['value'];
            return true;
          case 'setStringList':
            _sharedPreferencesData[methodCall.arguments['key']] =
                methodCall.arguments['value'];
            return true;
          case 'getBool':
            return _sharedPreferencesData[methodCall.arguments['key']] as bool?;
          case 'getInt':
            return _sharedPreferencesData[methodCall.arguments['key']] as int?;
          case 'getString':
            return _sharedPreferencesData[methodCall.arguments['key']]
                as String?;
          case 'getDouble':
            return _sharedPreferencesData[methodCall.arguments['key']]
                as double?;
          case 'getStringList':
            return _sharedPreferencesData[methodCall.arguments['key']]
                as List<String>?;
          case 'remove':
            _sharedPreferencesData.remove(methodCall.arguments['key']);
            return true;
          case 'clear':
            _sharedPreferencesData.clear();
            return true;
          case 'containsKey':
            return _sharedPreferencesData
                .containsKey(methodCall.arguments['key']);
          default:
            return null;
        }
      },
    );
  }

  /// Setup Local Notifications mocking
  static void _setupLocalNotificationsMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('dexterous.com/flutter/local_notifications'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'initialize':
            return true;
          case 'requestPermissions':
            return true;
          case 'show':
            return null;
          case 'cancel':
            return null;
          case 'cancelAll':
            return null;
          case 'getNotificationAppLaunchDetails':
            return {'notificationLaunchedApp': false};
          default:
            return null;
        }
      },
    );
  }

  /// Setup Path Provider mocking
  static void _setupPathProviderMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/path_provider'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'getTemporaryDirectory':
            return '/mock/temp/path';
          case 'getApplicationDocumentsDirectory':
            return '/mock/documents/path';
          case 'getApplicationSupportDirectory':
            return '/mock/support/path';
          case 'getExternalStorageDirectory':
            return '/mock/external/path';
          default:
            return '/mock/default/path';
        }
      },
    );
  }

  /// Setup Share Plus mocking
  static void _setupSharePlusMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('dev.fluttercommunity.plus/share'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'share':
            return null; // Simulate successful share
          case 'shareFiles':
            return null; // Simulate successful file share
          default:
            return null;
        }
      },
    );
  }

  /// Setup Connectivity Plus mocking
  static void _setupConnectivityPlusMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('dev.fluttercommunity.plus/connectivity'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'check':
            return 'wifi'; // Simulate WiFi connection
          case 'wifiName':
            return 'MockWiFi';
          case 'wifiBSSID':
            return '00:00:00:00:00:00';
          default:
            return null;
        }
      },
    );
  }

  /// Setup Speech to Text mocking
  static void _setupSpeechToTextMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugin.csdcorp.com/speech_to_text'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'initialize':
            return true;
          case 'listen':
            return true;
          case 'stop':
            return true;
          case 'cancel':
            return true;
          case 'hasPermission':
            return true;
          case 'locales':
            return [
              {'localeId': 'en_US', 'name': 'English (US)'},
              {'localeId': 'es_ES', 'name': 'Spanish (Spain)'},
            ];
          default:
            return null;
        }
      },
    );
  }

  /// Setup Firebase Core mocking
  static void _setupFirebaseCoreMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/firebase_core'),
      (MethodCall methodCall) async {
        switch (methodCall.method) {
          case 'Firebase#initializeCore':
            return [
              {
                'name': '[DEFAULT]',
                'options': {
                  'apiKey': 'mock-api-key',
                  'appId': 'mock-app-id',
                  'messagingSenderId': 'mock-sender-id',
                  'projectId': 'mock-project-id',
                },
                'pluginConstants': {},
              }
            ];
          case 'Firebase#initializeApp':
            return {
              'name': methodCall.arguments?['appName'] ?? '[DEFAULT]',
              'options': methodCall.arguments?['options'] ??
                  {
                    'apiKey': 'mock-api-key',
                    'appId': 'mock-app-id',
                    'messagingSenderId': 'mock-sender-id',
                    'projectId': 'mock-project-id',
                  },
              'pluginConstants': {},
            };
          case 'Firebase#app':
            return {
              'name': methodCall.arguments?['appName'] ?? '[DEFAULT]',
              'options': {
                'apiKey': 'mock-api-key',
                'appId': 'mock-app-id',
                'messagingSenderId': 'mock-sender-id',
                'projectId': 'mock-project-id',
              },
              'pluginConstants': {},
            };
          default:
            return null;
        }
      },
    );
  }

  // Cleanup methods for each platform channel
  static void _clearFirebaseAuthMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/firebase_auth'),
      null,
    );
  }

  static void _clearFirestoreMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/cloud_firestore'),
      null,
    );
  }

  static void _clearSharedPreferencesMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/shared_preferences'),
      null,
    );
  }

  static void _clearLocalNotificationsMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('dexterous.com/flutter/local_notifications'),
      null,
    );
  }

  static void _clearPathProviderMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/path_provider'),
      null,
    );
  }

  static void _clearSharePlusMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('dev.fluttercommunity.plus/share'),
      null,
    );
  }

  static void _clearConnectivityPlusMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('dev.fluttercommunity.plus/connectivity'),
      null,
    );
  }

  static void _clearSpeechToTextMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugin.csdcorp.com/speech_to_text'),
      null,
    );
  }

  static void _clearFirebaseCoreMocks() {
    TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
        .setMockMethodCallHandler(
      const MethodChannel('plugins.flutter.io/firebase_core'),
      null,
    );
  }

  /// Helper method to set custom SharedPreferences data for specific tests
  static void setSharedPreferencesData(String key, dynamic value) {
    _sharedPreferencesData[key] = value;
  }

  /// Helper method to get SharedPreferences data for test verification
  static dynamic getSharedPreferencesData(String key) {
    return _sharedPreferencesData[key];
  }

  /// Helper method to set Firebase Auth state for specific tests
  static void setFirebaseAuthState(
      {String? uid, String? email, bool isSignedIn = false}) {
    if (uid != null && email != null) {
      _firebaseAuthState['currentUser'] = {
        'uid': uid,
        'email': email,
        'displayName': 'Test User',
      };
    } else {
      _firebaseAuthState['currentUser'] = null;
    }
    _firebaseAuthState['isSignedIn'] = isSignedIn;
  }

  /// Helper method to get Firebase Auth state for test verification
  static Map<String, dynamic> getFirebaseAuthState() {
    return Map<String, dynamic>.from(_firebaseAuthState);
  }
}
