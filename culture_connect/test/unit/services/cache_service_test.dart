import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/services/cache_service.dart';

// Generate mocks
@GenerateMocks([])
void main() {
  late CacheService cacheService;

  setUp(() {
    cacheService = CacheService();

    // Set up SharedPreferences mock
    SharedPreferences.setMockInitialValues({});
  });

  group('CacheService', () {
    test('saveData should save data and timestamp', () async {
      // Arrange
      const testKey = 'test_key';
      final testData = {'name': 'Test', 'value': 123};

      // Act
      final result = await cacheService.saveData(testKey, testData);

      // Assert
      expect(result, isTrue);

      // Verify data was saved
      final prefs = await SharedPreferences.getInstance();
      expect(prefs.getString(testKey), isNotNull);
      expect(prefs.getInt('last_updated_$testKey'), isNotNull);
    });

    test('getData should return null if key does not exist', () async {
      // Arrange
      const testKey = 'nonexistent_key';

      // Act
      final result = await cacheService.getData(testKey);

      // Assert
      expect(result, isNull);
    });

    test('getData should return data if key exists and is not expired',
        () async {
      // Arrange
      const testKey = 'test_key';
      final testData = {'name': 'Test', 'value': 123};

      // Save data
      await cacheService.saveData(testKey, testData);

      // Act
      final result = await cacheService.getData(testKey);

      // Assert
      expect(result, isNotNull);
      expect(result['name'], equals('Test'));
      expect(result['value'], equals(123));
    });

    test('getData should return null if cache is expired', () async {
      // Arrange
      const testKey = 'test_key_expired';
      final testData = {'name': 'Test', 'value': 123};

      // Save data
      await cacheService.saveData(testKey, testData);

      // Manually set last updated timestamp to 25 hours ago
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      final expiredTime = now.subtract(const Duration(hours: 25));
      await prefs.setInt(
          'last_updated_$testKey', expiredTime.millisecondsSinceEpoch);

      // Clear memory cache to force disk read
      // Create a new cache service instance to avoid memory cache interference
      final freshCacheService = CacheService();

      // Act
      final result = await freshCacheService.getData(testKey);

      // Assert
      expect(result, isNull);
    });

    test('clearCache should remove data and timestamp', () async {
      // Arrange
      const testKey = 'test_key';
      final testData = {'name': 'Test', 'value': 123};

      // Save data
      await cacheService.saveData(testKey, testData);

      // Act
      final result = await cacheService.clearCache(testKey);

      // Assert
      expect(result, isTrue);

      // Verify data was removed
      final prefs = await SharedPreferences.getInstance();
      expect(prefs.getString(testKey), isNull);
      expect(prefs.getInt('last_updated_$testKey'), isNull);
    });

    test('clearAllCache should remove all cache data', () async {
      // Arrange
      await cacheService.saveData('key1', {'data': 1});
      await cacheService.saveData('key2', {'data': 2});
      await cacheService.saveData(cacheService.bookingsKey, {'bookings': []});

      // Act
      final result = await cacheService.clearAllCache();

      // Assert
      expect(result, isTrue);

      // Verify specific keys were removed
      final prefs = await SharedPreferences.getInstance();
      expect(prefs.getString(cacheService.bookingsKey), isNull);
      expect(prefs.getString(cacheService.experiencesKey), isNull);
      expect(prefs.getString(cacheService.reviewsKey), isNull);

      // Other keys should still exist
      expect(prefs.getString('key1'), isNotNull);
      expect(prefs.getString('key2'), isNotNull);
    });

    test('isCacheAvailable should return false if key does not exist',
        () async {
      // Arrange
      const testKey = 'nonexistent_key';

      // Act
      final result = await cacheService.isCacheAvailable(testKey);

      // Assert
      expect(result, isFalse);
    });

    test('isCacheAvailable should return true if cache is not expired',
        () async {
      // Arrange
      const testKey = 'test_key';
      final testData = {'name': 'Test', 'value': 123};

      // Save data
      await cacheService.saveData(testKey, testData);

      // Act
      final result = await cacheService.isCacheAvailable(testKey);

      // Assert
      expect(result, isTrue);
    });

    test('isCacheAvailable should return false if cache is expired', () async {
      // Arrange
      const testKey = 'test_key';
      final testData = {'name': 'Test', 'value': 123};

      // Save data
      await cacheService.saveData(testKey, testData);

      // Manually set last updated timestamp to 25 hours ago
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now();
      final expiredTime = now.subtract(const Duration(hours: 25));
      await prefs.setInt(
          'last_updated_$testKey', expiredTime.millisecondsSinceEpoch);

      // Act
      final result = await cacheService.isCacheAvailable(testKey);

      // Assert
      expect(result, isFalse);
    });
  });
}
