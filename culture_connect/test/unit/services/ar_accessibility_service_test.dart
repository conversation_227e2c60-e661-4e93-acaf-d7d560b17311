import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/services.dart';
import 'package:culture_connect/services/ar_accessibility_service.dart';

// Generate mocks for dependencies
@GenerateMocks([SharedPreferences])
import 'ar_accessibility_service_test.mocks.dart';

void main() {
  late ARAccessibilityService service;
  late MockSharedPreferences mockPreferences;

  setUp(() {
    mockPreferences = MockSharedPreferences();

    // Setup mock preferences
    when(mockPreferences.getBool('high_contrast_mode')).thenReturn(false);
    when(mockPreferences.getBool('screen_reader_enabled')).thenReturn(false);
    when(mockPreferences.getBool('reduced_motion')).thenReturn(false);
    when(mockPreferences.getBool('simplified_gestures')).thenReturn(false);
    when(mockPreferences.getBool('audio_guidance')).thenReturn(false);
    when(mockPreferences.setString(any, any)).thenAnswer((_) async => true);
    when(mockPreferences.setBool(any, any)).thenAnswer((_) async => true);

    // Create service instance
    service = ARAccessibilityService(preferences: mockPreferences);
  });

  group('ARAccessibilityService Tests', () {
    test('initialize should load settings from preferences', () async {
      // Arrange
      when(mockPreferences.getBool('high_contrast_mode')).thenReturn(true);
      when(mockPreferences.getBool('screen_reader_enabled')).thenReturn(true);

      // Act
      await service.initialize();

      // Assert
      expect(service.isHighContrastModeEnabled, true);
      expect(service.isScreenReaderEnabled, true);
      verify(mockPreferences.getBool('high_contrast_mode')).called(1);
      verify(mockPreferences.getBool('screen_reader_enabled')).called(1);
    });

    test('toggleHighContrastMode should toggle high contrast mode', () async {
      // Arrange
      await service.initialize();
      final initialValue = service.isHighContrastModeEnabled;

      // Act
      await service.toggleHighContrastMode();

      // Assert
      expect(service.isHighContrastModeEnabled, !initialValue);
      verify(mockPreferences.setBool('high_contrast_mode', !initialValue))
          .called(1);
    });

    test('toggleScreenReader should toggle screen reader', () async {
      // Arrange
      await service.initialize();
      final initialValue = service.isScreenReaderEnabled;

      // Act
      service.toggleScreenReader();

      // Assert
      expect(service.isScreenReaderEnabled, !initialValue);
      verify(mockPreferences.setBool('screen_reader_enabled', !initialValue))
          .called(1);
    });

    test('toggleReducedMotion should toggle reduced motion', () async {
      // Arrange
      await service.initialize();
      final initialValue = service.isReducedMotionEnabled;

      // Act
      service.toggleReducedMotion();

      // Assert
      expect(service.isReducedMotionEnabled, !initialValue);
      verify(mockPreferences.setBool('reduced_motion', !initialValue))
          .called(1);
    });

    test('toggleGestureSimplification should toggle gesture simplification',
        () async {
      // Arrange
      await service.initialize();
      final initialValue = service.isGestureSimplificationEnabled;

      // Act
      service.toggleGestureSimplification(!initialValue);

      // Assert
      expect(service.isGestureSimplificationEnabled, !initialValue);
    });

    test('toggleAudioGuidance should toggle audio guidance', () async {
      // Arrange
      await service.initialize();
      final initialValue = service.isAudioGuidanceEnabled;

      // Act
      service.toggleAudioGuidance(!initialValue);

      // Assert
      expect(service.isAudioGuidanceEnabled, !initialValue);
    });

    test('toggleHighContrast should toggle high contrast mode', () async {
      // Arrange
      await service.initialize();
      final initialValue = service.isHighContrastEnabled;

      // Act
      service.toggleHighContrast(!initialValue);

      // Assert
      expect(service.isHighContrastEnabled, !initialValue);
    });

    test('getAccessibleIconSize should return appropriate icon size', () async {
      // Arrange
      await service.initialize();
      const baseSize = 24.0;

      // Act
      final iconSize = service.getAccessibleIconSize(baseSize);

      // Assert
      expect(iconSize, isA<double>());
      expect(iconSize >= baseSize, true);
    });

    test('getAccessibleAnimationDuration should return appropriate duration',
        () async {
      // Arrange
      await service.initialize();
      const baseDuration = Duration(milliseconds: 300);

      // Act
      final accessibleDuration =
          service.getAccessibleAnimationDuration(baseDuration);

      // Assert
      expect(accessibleDuration, isA<Duration>());
    });
  });
}
