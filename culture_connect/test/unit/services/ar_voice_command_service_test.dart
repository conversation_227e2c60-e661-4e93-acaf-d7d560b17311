import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/services/ar_voice_command_service.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late ARVoiceCommandService service;

  setUp(() {
    // Create service instance (singleton)
    service = ARVoiceCommandService();
  });

  group('ARVoiceCommandService Tests', () {
    test('initialize should setup voice commands', () async {
      // Act
      final result = await service.initialize();

      // Assert
      expect(result, isA<bool>());
    });

    test('service should have correct initial state', () {
      // Assert
      expect(service.isListening, false);
      expect(service.lastRecognizedWords, '');
      expect(service.confidence, 0.0);
      expect(service.commandHistory, isEmpty);
    });

    test('registerCommand should register a command handler', () {
      // Arrange
      bool commandExecuted = false;

      // Act
      service.registerCommand('test command', () {
        commandExecuted = true;
      });

      // Assert - We can't directly test this without triggering speech recognition
      // but we can verify the method doesn't throw
      expect(commandExecuted, false); // Not executed yet
    });

    test('availableCommands should contain expected commands', () {
      // Assert
      expect(ARVoiceCommandService.availableCommands, contains('zoom in'));
      expect(ARVoiceCommandService.availableCommands, contains('zoom out'));
      expect(ARVoiceCommandService.availableCommands, contains('rotate left'));
      expect(ARVoiceCommandService.availableCommands, contains('rotate right'));
      expect(ARVoiceCommandService.availableCommands, contains('show info'));
    });

    test('startListening should return a boolean', () async {
      // Act
      final result = await service.startListening();

      // Assert
      expect(result, isA<bool>());
    });

    test('stopListening should return a boolean', () async {
      // Act
      final result = await service.stopListening();

      // Assert
      expect(result, isA<bool>());
    });

    test('cancelListening should return a boolean', () async {
      // Act
      final result = await service.cancelListening();

      // Assert
      expect(result, isA<bool>());
    });
  });
}
