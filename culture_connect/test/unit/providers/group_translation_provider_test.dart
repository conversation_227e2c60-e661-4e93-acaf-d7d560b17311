import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/message_model.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/message_translation_metadata.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/models/translation/translation_confidence_model.dart';
import 'package:culture_connect/services/group_translation_service.dart';
import 'package:culture_connect/services/language_detection_service.dart';
import 'package:culture_connect/providers/group_translation_provider.dart';

// Manual mock classes
class MockGroupTranslationService extends Mock
    implements GroupTranslationService {}

class MockLanguageDetectionService extends Mock
    implements LanguageDetectionService {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  late MockGroupTranslationService mockGroupTranslationService;
  late MockLanguageDetectionService mockLanguageDetectionService;
  late ProviderContainer container;

  // Sample data for testing
  const sampleGroupId = 'group-123';
  const sampleUserId = 'user-456';
  const sampleMessageId = 'message-789';

  const sampleLanguage = LanguageModel(
    code: 'en',
    name: 'English',
    flag: '🇺🇸',
  );

  const sampleParticipantPreference = ParticipantLanguagePreference(
    userId: sampleUserId,
    displayName: 'Test User',
    preferredLanguage: sampleLanguage,
  );

  const sampleGroupSettings = GroupTranslationSettings(
    groupId: sampleGroupId,
    participantPreferences: {
      sampleUserId: sampleParticipantPreference,
    },
  );

  final sampleMessage = MessageModel(
    id: sampleMessageId,
    chatId: sampleGroupId,
    senderId: 'sender-123',
    recipientId: '',
    text: 'Hello, world!',
    timestamp: DateTime(2023, 1, 1),
    status: MessageStatus.sent,
    type: MessageType.text,
    originalLanguage: 'en',
  );

  final sampleTranslationMetadata = MessageTranslationMetadata(
    originalText: 'Hello, world!',
    translatedText: 'Bonjour, monde!',
    sourceLanguage: 'en',
    targetLanguage: 'fr',
    translatedAt: DateTime(2023, 1, 1),
    confidence: const TranslationConfidenceModel(
      overallScore: 0.95,
      confidenceLevel: ConfidenceLevel.high,
    ),
    culturalContext: null,
    slangIdiom: null,
    pronunciation: null,
  );

  final sampleGroupMessageTranslation = GroupMessageTranslation(
    messageId: sampleMessageId,
    originalText: 'Hello, world!',
    originalLanguageCode: 'en',
    translations: {
      'fr': 'Bonjour, monde!',
      'es': '¡Hola, mundo!',
    },
    translatedAt: DateTime.now(),
  );

  const sampleSupportedLanguages = [
    LanguageModel(code: 'en', name: 'English', flag: '🇺🇸'),
    LanguageModel(code: 'fr', name: 'French', flag: '🇫🇷'),
    LanguageModel(code: 'es', name: 'Spanish', flag: '🇪🇸'),
  ];

  setUp(() {
    mockGroupTranslationService = MockGroupTranslationService();
    mockLanguageDetectionService = MockLanguageDetectionService();

    // Setup mock GroupTranslationService
    when(mockGroupTranslationService.getGroupTranslationSettings(sampleGroupId))
        .thenAnswer((_) async => sampleGroupSettings);
    when(mockGroupTranslationService
            .getGroupMessageTranslation(sampleMessageId))
        .thenAnswer((_) async => sampleGroupMessageTranslation);
    when(mockGroupTranslationService.getTranslationForUser(
      sampleMessage,
      sampleUserId,
      sampleGroupSettings,
    )).thenAnswer((_) async => 'Bonjour, monde!');
    when(mockGroupTranslationService.getTranslationMetadataForUser(
      sampleMessage,
      sampleUserId,
      sampleGroupSettings,
    )).thenAnswer((_) async => sampleTranslationMetadata);
    when(mockGroupTranslationService.translateGroupMessage(
      sampleMessage,
      sampleGroupSettings,
    )).thenAnswer((_) async => sampleGroupMessageTranslation);

    // Setup mock LanguageDetectionService
    when(mockLanguageDetectionService.supportedLanguages)
        .thenReturn(sampleSupportedLanguages);
    when(mockLanguageDetectionService.detectLanguage('test'))
        .thenAnswer((_) async => 'en');

    // Create a ProviderContainer with overrides
    container = ProviderContainer(
      overrides: [
        groupTranslationServiceProvider
            .overrideWithValue(mockGroupTranslationService),
        languageDetectionServiceProvider
            .overrideWithValue(mockLanguageDetectionService),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  group('GroupTranslationProvider Tests', () {
    test('groupTranslationSettingsProvider should return group settings',
        () async {
      // Act
      final settings = await container
          .read(groupTranslationSettingsProvider(sampleGroupId).future);

      // Assert
      expect(settings.groupId, sampleGroupId);
      expect(settings.participantPreferences[sampleUserId], isNotNull);
      expect(
          settings.participantPreferences[sampleUserId]!.userId, sampleUserId);

      // Verify service was called
      verify(mockGroupTranslationService
              .getGroupTranslationSettings(sampleGroupId))
          .called(1);
    });

    test('groupMessageTranslationProvider should return message translation',
        () async {
      // Act
      final translation = await container
          .read(groupMessageTranslationProvider(sampleMessageId).future);

      // Assert
      expect(translation, isNotNull);
      expect(translation!.messageId, sampleMessageId);
      expect(translation.originalText, 'Hello, world!');
      expect(translation.translations['fr'], 'Bonjour, monde!');

      // Verify service was called
      verify(mockGroupTranslationService
              .getGroupMessageTranslation(sampleMessageId))
          .called(1);
    });

    test('translatedTextForUserProvider should return translated text',
        () async {
      // Act
      final translatedText = await container.read(
        translatedTextForUserProvider((
          message: sampleMessage,
          userId: sampleUserId,
          groupId: sampleGroupId,
        )).future,
      );

      // Assert
      expect(translatedText, 'Bonjour, monde!');

      // Verify service was called
      verify(mockGroupTranslationService
              .getGroupTranslationSettings(sampleGroupId))
          .called(1);
      verify(mockGroupTranslationService.getTranslationForUser(
        sampleMessage,
        sampleUserId,
        sampleGroupSettings,
      )).called(1);
    });

    test(
        'translationMetadataForUserProvider should return translation metadata',
        () async {
      // Act
      final metadata = await container.read(
        translationMetadataForUserProvider((
          message: sampleMessage,
          userId: sampleUserId,
          groupId: sampleGroupId,
        )).future,
      );

      // Assert
      expect(metadata, isNotNull);
      expect(metadata!.originalText, 'Hello, world!');
      expect(metadata.translatedText, 'Bonjour, monde!');
      expect(metadata.sourceLanguage, 'en');
      expect(metadata.targetLanguage, 'fr');

      // Verify service was called
      verify(mockGroupTranslationService
              .getGroupTranslationSettings(sampleGroupId))
          .called(1);
      verify(mockGroupTranslationService.getTranslationMetadataForUser(
        sampleMessage,
        sampleUserId,
        sampleGroupSettings,
      )).called(1);
    });

    test(
        'participantLanguagePreferencesProvider should return participant preferences',
        () async {
      // Act
      final preferences = await container.read(
        participantLanguagePreferencesProvider(sampleGroupId).future,
      );

      // Assert
      expect(preferences, isNotNull);
      expect(preferences[sampleUserId], isNotNull);
      expect(preferences[sampleUserId]!.userId, sampleUserId);

      // Verify service was called
      verify(mockGroupTranslationService
              .getGroupTranslationSettings(sampleGroupId))
          .called(1);
    });

    test(
        'participantLanguagePreferenceProvider should return specific participant preference',
        () async {
      // Act
      final preference = await container.read(
        participantLanguagePreferenceProvider((
          groupId: sampleGroupId,
          userId: sampleUserId,
        )).future,
      );

      // Assert
      expect(preference, isNotNull);
      expect(preference!.userId, sampleUserId);
      expect(preference.preferredLanguage.code, 'en');

      // Verify service was called
      verify(mockGroupTranslationService
              .getGroupTranslationSettings(sampleGroupId))
          .called(1);
    });

    test('uniqueGroupLanguagesProvider should return unique languages',
        () async {
      // Act
      final languages = await container.read(
        uniqueGroupLanguagesProvider(sampleGroupId).future,
      );

      // Assert
      expect(languages, isNotNull);
      expect(languages.length, 1);
      expect(languages[0].code, 'en');

      // Verify service was called
      verify(mockGroupTranslationService
              .getGroupTranslationSettings(sampleGroupId))
          .called(1);
    });

    test('supportedLanguagesProvider should return supported languages', () {
      // Act
      final languages = container.read(supportedLanguagesProvider);

      // Assert
      expect(languages, isNotNull);
      expect(languages.length, 3);
      expect(languages[0].code, 'en');
      expect(languages[1].code, 'fr');
      expect(languages[2].code, 'es');

      // Verify service was called
      verify(mockLanguageDetectionService.supportedLanguages).called(1);
    });

    test('detectedLanguageProvider should return detected language', () async {
      // Act
      final language = await container.read(
        detectedLanguageProvider('Hello, world!').future,
      );

      // Assert
      expect(language, 'en');

      // Verify service was called
      verify(mockLanguageDetectionService.detectLanguage('Hello, world!'))
          .called(1);
    });
  });
}
