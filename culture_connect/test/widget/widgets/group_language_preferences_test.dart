import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:culture_connect/models/translation/language_model.dart';
import 'package:culture_connect/models/translation/group_translation_model.dart';
import 'package:culture_connect/providers/group_translation_provider.dart';
import 'package:culture_connect/widgets/messaging/group_language_preferences.dart';
import 'package:culture_connect/theme/app_theme.dart';

// Manual mock classes
class MockGroupTranslationNotifier extends StateNotifier<AsyncValue<void>> {
  MockGroupTranslationNotifier() : super(const AsyncValue.data(null));

  Future<void> updateParticipantPreference(
    String groupId,
    ParticipantLanguagePreference preference,
  ) async {
    // Mock implementation
  }

  Future<void> toggleAutoTranslateForParticipant(
    String groupId,
    String userId,
    bool value,
  ) async {
    // Mock implementation
  }

  Future<void> toggleShowOriginalTextForParticipant(
    String groupId,
    String userId,
    bool value,
  ) async {
    // Mock implementation
  }

  Future<void> toggleFeatureForGroup(
    String groupId,
    String feature,
    bool value,
  ) async {
    // Mock implementation
  }

  Future<void> clearGroupTranslationCache(String groupId) async {
    // Mock implementation
  }
}

void main() {
  late ProviderContainer container;
  late MockGroupTranslationNotifier mockGroupTranslationNotifier;

  // Sample data for testing
  final sampleGroupId = 'group-123';
  final sampleUserId = 'user-456';

  final sampleLanguages = [
    const LanguageModel(code: 'en', name: 'English', flag: '🇺🇸'),
    const LanguageModel(code: 'fr', name: 'French', flag: '🇫🇷'),
    const LanguageModel(code: 'es', name: 'Spanish', flag: '🇪🇸'),
  ];

  final sampleParticipantPreference = ParticipantLanguagePreference(
    userId: sampleUserId,
    displayName: 'Test User',
    preferredLanguage:
        const LanguageModel(code: 'en', name: 'English', flag: '🇺🇸'),
    autoTranslate: true,
    showOriginalText: false,
    useDialectRecognition: false,
  );

  final sampleGroupSettings = GroupTranslationSettings(
    groupId: sampleGroupId,
    participantPreferences: {
      sampleUserId: sampleParticipantPreference,
      'user-789': ParticipantLanguagePreference(
        userId: 'user-789',
        displayName: 'Other User',
        preferredLanguage:
            const LanguageModel(code: 'fr', name: 'French', flag: '🇫🇷'),
      ),
    },
    autoDetectLanguages: true,
    showTranslationIndicators: true,
    enableRealTimeTranslation: true,
    translateMediaCaptions: true,
    enableCulturalContext: true,
    enableSlangIdiomDetection: true,
    enablePronunciationGuidance: true,
  );

  setUp(() {
    mockGroupTranslationNotifier = MockGroupTranslationNotifier();

    // Create a ProviderContainer with overrides
    container = ProviderContainer(
      overrides: [
        // Override group translation providers
        groupTranslationSettingsProvider(sampleGroupId)
            .overrideWith((ref) => Future.value(sampleGroupSettings)),

        participantLanguagePreferenceProvider((
          groupId: sampleGroupId,
          userId: sampleUserId,
        )).overrideWith((ref) => Future.value(sampleParticipantPreference)),

        supportedLanguagesProvider.overrideWith((ref) => sampleLanguages),
      ],
    );
  });

  tearDown(() {
    container.dispose();
  });

  group('GroupLanguagePreferences Widget Tests', () {
    testWidgets('should display user language preferences',
        (WidgetTester tester) async {
      // Build our widget
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: GroupLanguagePreferences(
                groupId: sampleGroupId,
              ),
            ),
          ),
        ),
      );

      // Initial build will show loading state
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // Wait for async operations to complete
      await tester.pumpAndSettle();

      // Should show section titles
      expect(find.text('Your Language Preferences'), findsOneWidget);
      expect(find.text('Group Translation Settings'), findsOneWidget);
      expect(find.text('Enhanced Translation Features'), findsOneWidget);

      // Should show language selector
      expect(find.text('Preferred Language'), findsOneWidget);

      // Should show toggle switches
      expect(find.text('Auto-Translate Messages'), findsOneWidget);
      expect(find.text('Show Original Text'), findsOneWidget);
      expect(find.text('Use Dialect Recognition'), findsOneWidget);

      // Should show group settings
      expect(find.text('Auto-Detect Languages'), findsOneWidget);
      expect(find.text('Show Translation Indicators'), findsOneWidget);
      expect(find.text('Real-Time Translation'), findsOneWidget);

      // Should show enhanced features
      expect(find.text('Cultural Context'), findsOneWidget);
      expect(find.text('Slang & Idiom Detection'), findsOneWidget);
      expect(find.text('Pronunciation Guidance'), findsOneWidget);

      // Should show languages used in group
      expect(find.text('Languages Used in This Group'), findsOneWidget);

      // Should show clear cache button
      expect(find.text('Clear Translation Cache'), findsOneWidget);
    });

    testWidgets('should toggle auto-translate when switch is tapped',
        (WidgetTester tester) async {
      // Setup mock
      when(mockGroupTranslationNotifier.toggleAutoTranslateForParticipant(
        sampleGroupId,
        sampleUserId,
        false,
      )).thenAnswer((_) async {});

      // Build our widget
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: GroupLanguagePreferences(
                groupId: sampleGroupId,
              ),
            ),
          ),
        ),
      );

      // Wait for async operations to complete
      await tester.pumpAndSettle();

      // Find the auto-translate switch
      final autoTranslateSwitch = find.byType(Switch).first;

      // Tap the switch
      await tester.tap(autoTranslateSwitch);
      await tester.pumpAndSettle();

      // Verify the notifier was called
      verify(mockGroupTranslationNotifier.toggleAutoTranslateForParticipant(
        sampleGroupId,
        sampleUserId,
        false,
      )).called(1);
    });

    testWidgets('should toggle show original text when switch is tapped',
        (WidgetTester tester) async {
      // Setup mock
      when(mockGroupTranslationNotifier.toggleShowOriginalTextForParticipant(
        sampleGroupId,
        sampleUserId,
        true,
      )).thenAnswer((_) async {});

      // Build our widget
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: GroupLanguagePreferences(
                groupId: sampleGroupId,
              ),
            ),
          ),
        ),
      );

      // Wait for async operations to complete
      await tester.pumpAndSettle();

      // Find the show original text switch
      final showOriginalSwitch = find.byType(Switch).at(1);

      // Tap the switch
      await tester.tap(showOriginalSwitch);
      await tester.pumpAndSettle();

      // Verify the notifier was called
      verify(mockGroupTranslationNotifier.toggleShowOriginalTextForParticipant(
        sampleGroupId,
        sampleUserId,
        true,
      )).called(1);
    });

    testWidgets('should toggle group feature when switch is tapped',
        (WidgetTester tester) async {
      // Setup mock
      when(mockGroupTranslationNotifier.toggleFeatureForGroup(
        sampleGroupId,
        'autoDetectLanguages',
        false,
      )).thenAnswer((_) async {});

      // Build our widget
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: GroupLanguagePreferences(
                groupId: sampleGroupId,
              ),
            ),
          ),
        ),
      );

      // Wait for async operations to complete
      await tester.pumpAndSettle();

      // Find the auto-detect languages switch
      final autoDetectSwitch = find.byType(Switch).at(3);

      // Tap the switch
      await tester.tap(autoDetectSwitch);
      await tester.pumpAndSettle();

      // Verify the notifier was called
      verify(mockGroupTranslationNotifier.toggleFeatureForGroup(
        sampleGroupId,
        'autoDetectLanguages',
        false,
      )).called(1);
    });

    testWidgets('should clear cache when button is tapped',
        (WidgetTester tester) async {
      // Setup mock
      when(mockGroupTranslationNotifier
              .clearGroupTranslationCache(sampleGroupId))
          .thenAnswer((_) async {});

      // Build our widget
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: GroupLanguagePreferences(
                groupId: sampleGroupId,
              ),
            ),
          ),
        ),
      );

      // Wait for async operations to complete
      await tester.pumpAndSettle();

      // Find the clear cache button
      final clearCacheButton = find.text('Clear Translation Cache');

      // Tap the button
      await tester.tap(clearCacheButton);
      await tester.pumpAndSettle();

      // Verify the notifier was called
      verify(mockGroupTranslationNotifier
              .clearGroupTranslationCache(sampleGroupId))
          .called(1);
    });

    testWidgets('should display language statistics',
        (WidgetTester tester) async {
      // Build our widget
      await tester.pumpWidget(
        UncontrolledProviderScope(
          container: container,
          child: MaterialApp(
            theme: AppTheme.lightTheme,
            home: Scaffold(
              body: GroupLanguagePreferences(
                groupId: sampleGroupId,
              ),
            ),
          ),
        ),
      );

      // Wait for async operations to complete
      await tester.pumpAndSettle();

      // Should show language statistics
      expect(find.text('Languages Used in This Group'), findsOneWidget);
      expect(find.text('English'), findsAtLeastNWidgets(1));
      expect(find.text('French'), findsAtLeastNWidgets(1));
      expect(
          find.text('1 member using this language'), findsAtLeastNWidgets(1));
    });
  });
}
