import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:culture_connect/widgets/experience_card.dart';

void main() {
  group('ExperienceCard Widget Tests', () {
    const testTitle = 'Test Experience';
    const testLocation = 'Test Location';
    const testImageUrl = 'https://example.com/test-image.jpg';
    const testRating = 4.5;
    const testPrice = '\$50';
    const testDuration = '2 hours';

    Widget createTestWidget({
      String title = testTitle,
      String location = testLocation,
      String imageUrl = testImageUrl,
      double? rating = testRating,
      String? price = testPrice,
      String? duration = testDuration,
      VoidCallback? onTap,
    }) {
      return MaterialApp(
        home: Scaffold(
          body: ExperienceCard(
            title: title,
            location: location,
            imageUrl: imageUrl,
            rating: rating,
            price: price,
            duration: duration,
            onTap: onTap,
          ),
        ),
      );
    }

    group('Widget Rendering', () {
      testWidgets('should render all required elements', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Assert
        expect(find.text(testTitle), findsOneWidget);
        expect(find.text(testLocation), findsOneWidget);
        expect(find.text(testPrice), findsOneWidget);
        expect(find.text(testDuration), findsOneWidget);
        expect(find.byType(CachedNetworkImage), findsOneWidget);
      });

      testWidgets('should render without optional parameters', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(
          rating: null,
          price: null,
          duration: null,
        ));
        
        // Assert
        expect(find.text(testTitle), findsOneWidget);
        expect(find.text(testLocation), findsOneWidget);
        expect(find.text(testPrice), findsNothing);
        expect(find.text(testDuration), findsNothing);
      });

      testWidgets('should display rating when provided', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(rating: 4.5));
        
        // Assert
        expect(find.byIcon(Icons.star), findsWidgets);
        expect(find.text('4.5'), findsOneWidget);
      });

      testWidgets('should not display rating when null', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(rating: null));
        
        // Assert
        expect(find.byIcon(Icons.star), findsNothing);
      });
    });

    group('Optimized Image Loading', () {
      testWidgets('should use CachedNetworkImage with optimized settings', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Assert
        final cachedImageWidget = tester.widget<CachedNetworkImage>(
          find.byType(CachedNetworkImage)
        );
        
        expect(cachedImageWidget.imageUrl, equals(testImageUrl));
        expect(cachedImageWidget.width, equals(100));
        expect(cachedImageWidget.height, equals(100));
        expect(cachedImageWidget.fit, equals(BoxFit.cover));
        
        // Verify optimized cache settings
        expect(cachedImageWidget.memCacheWidth, equals(150));
        expect(cachedImageWidget.memCacheHeight, equals(150));
        expect(cachedImageWidget.maxWidthDiskCache, equals(300));
        expect(cachedImageWidget.maxHeightDiskCache, equals(300));
        
        // Verify optimized fade durations
        expect(cachedImageWidget.fadeInDuration, equals(const Duration(milliseconds: 150)));
        expect(cachedImageWidget.fadeOutDuration, equals(const Duration(milliseconds: 75)));
        
        // Verify cache key optimization
        expect(cachedImageWidget.cacheKey, contains('exp_'));
      });

      testWidgets('should display placeholder while loading', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Assert - Should find placeholder container
        expect(find.byType(Container), findsWidgets);
      });

      testWidgets('should display error widget on image load failure', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(imageUrl: 'invalid-url'));
        
        // Assert - Should handle error gracefully
        expect(find.byType(CachedNetworkImage), findsOneWidget);
      });
    });

    group('Animation Performance', () {
      testWidgets('should have animation controller', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Assert - Widget should render without animation issues
        expect(find.byType(ExperienceCard), findsOneWidget);
        
        // Verify no animation errors during build
        await tester.pumpAndSettle();
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle tap animations smoothly', (WidgetTester tester) async {
        // Arrange
        bool tapped = false;
        
        // Act
        await tester.pumpWidget(createTestWidget(
          onTap: () => tapped = true,
        ));
        
        // Tap the card
        await tester.tap(find.byType(ExperienceCard));
        await tester.pumpAndSettle();
        
        // Assert
        expect(tapped, isTrue);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should dispose animation controller properly', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Remove widget
        await tester.pumpWidget(const MaterialApp(home: Scaffold()));
        
        // Assert - Should dispose without memory leaks
        expect(tester.takeException(), isNull);
      });
    });

    group('Interaction Handling', () {
      testWidgets('should handle tap events', (WidgetTester tester) async {
        // Arrange
        bool tapped = false;
        
        // Act
        await tester.pumpWidget(createTestWidget(
          onTap: () => tapped = true,
        ));
        
        await tester.tap(find.byType(ExperienceCard));
        
        // Assert
        expect(tapped, isTrue);
      });

      testWidgets('should handle tap when onTap is null', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(onTap: null));
        
        // Assert - Should not throw when tapping
        expect(() => tester.tap(find.byType(ExperienceCard)), returnsNormally);
      });

      testWidgets('should be accessible for screen readers', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Assert - Should have semantic information
        expect(find.byType(ExperienceCard), findsOneWidget);
        
        // Verify semantic structure
        final semantics = tester.getSemantics(find.byType(ExperienceCard));
        expect(semantics, isNotNull);
      });
    });

    group('Layout and Styling', () {
      testWidgets('should have proper card elevation', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Assert
        final card = tester.widget<Card>(find.byType(Card));
        expect(card.elevation, equals(4));
      });

      testWidgets('should have rounded corners', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Assert - Should find ClipRRect for rounded corners
        expect(find.byType(ClipRRect), findsWidgets);
      });

      testWidgets('should layout elements correctly', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget());
        
        // Assert - Should have proper layout structure
        expect(find.byType(Row), findsWidgets);
        expect(find.byType(Column), findsWidgets);
        expect(find.byType(Expanded), findsWidgets);
      });
    });

    group('Performance Validation', () {
      testWidgets('should render efficiently', (WidgetTester tester) async {
        // Arrange
        final stopwatch = Stopwatch()..start();
        
        // Act
        await tester.pumpWidget(createTestWidget());
        await tester.pumpAndSettle();
        
        stopwatch.stop();
        
        // Assert - Should render quickly
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
      });

      testWidgets('should handle multiple cards efficiently', (WidgetTester tester) async {
        // Arrange
        final cards = List.generate(10, (i) => ExperienceCard(
          title: 'Experience $i',
          location: 'Location $i',
          imageUrl: 'https://example.com/image$i.jpg',
          rating: 4.0 + (i % 5) * 0.2,
          price: '\$${50 + i * 10}',
          duration: '${2 + i % 3} hours',
        ));
        
        // Act
        await tester.pumpWidget(MaterialApp(
          home: Scaffold(
            body: ListView(children: cards),
          ),
        ));
        
        // Assert - Should render multiple cards without issues
        expect(find.byType(ExperienceCard), findsNWidgets(10));
        expect(tester.takeException(), isNull);
      });

      testWidgets('should not cause memory leaks', (WidgetTester tester) async {
        // Act - Create and destroy widget multiple times
        for (int i = 0; i < 5; i++) {
          await tester.pumpWidget(createTestWidget());
          await tester.pumpWidget(const MaterialApp(home: Scaffold()));
        }
        
        // Assert - Should not accumulate memory leaks
        expect(tester.takeException(), isNull);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle empty strings gracefully', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(
          title: '',
          location: '',
          price: '',
          duration: '',
        ));
        
        // Assert - Should render without throwing
        expect(find.byType(ExperienceCard), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle very long text', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(
          title: 'Very Long Experience Title That Might Overflow' * 5,
          location: 'Very Long Location Name That Might Cause Issues' * 3,
        ));
        
        // Assert - Should handle long text without overflow
        expect(find.byType(ExperienceCard), findsOneWidget);
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle extreme rating values', (WidgetTester tester) async {
        // Act
        await tester.pumpWidget(createTestWidget(rating: 0.0));
        await tester.pumpAndSettle();
        
        await tester.pumpWidget(createTestWidget(rating: 5.0));
        await tester.pumpAndSettle();
        
        // Assert - Should handle edge rating values
        expect(tester.takeException(), isNull);
      });
    });

    group('Theme Integration', () {
      testWidgets('should adapt to different themes', (WidgetTester tester) async {
        // Test light theme
        await tester.pumpWidget(MaterialApp(
          theme: ThemeData.light(),
          home: Scaffold(
            body: ExperienceCard(
              title: testTitle,
              location: testLocation,
              imageUrl: testImageUrl,
            ),
          ),
        ));
        
        expect(find.byType(ExperienceCard), findsOneWidget);
        
        // Test dark theme
        await tester.pumpWidget(MaterialApp(
          theme: ThemeData.dark(),
          home: Scaffold(
            body: ExperienceCard(
              title: testTitle,
              location: testLocation,
              imageUrl: testImageUrl,
            ),
          ),
        ));
        
        expect(find.byType(ExperienceCard), findsOneWidget);
        expect(tester.takeException(), isNull);
      });
    });
  });
}
