import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:culture_connect/services/progressive_loading_service.dart';

void main() {
  group('ProgressiveLoadingService Tests', () {
    late ProgressiveLoadingService loadingService;

    setUp(() {
      loadingService = ProgressiveLoadingService();
    });

    tearDown(() {
      loadingService.dispose();
    });

    group('Service Management', () {
      test('should create loader successfully', () {
        // Arrange
        final testItems = ['item1', 'item2', 'item3'];
        Widget itemBuilder(String item) => Text(item);

        // Act
        final loader = loadingService.createLoader<String>(
          loaderId: 'test_loader',
          items: testItems,
          itemBuilder: itemBuilder,
        );

        // Assert
        expect(loader, isA<ProgressiveLoader<String>>());
        expect(loader.id, equals('test_loader'));
      });

      test('should replace existing loader with same ID', () {
        // Arrange
        final items1 = ['a', 'b'];
        final items2 = ['x', 'y', 'z'];
        Widget itemBuilder(String item) => Text(item);

        // Act
        final loader1 = loadingService.createLoader<String>(
          loaderId: 'same_id',
          items: items1,
          itemBuilder: itemBuilder,
        );

        final loader2 = loadingService.createLoader<String>(
          loaderId: 'same_id',
          items: items2,
          itemBuilder: itemBuilder,
        );

        // Assert
        expect(loader1, isNot(equals(loader2)));
        expect(loadingService.getLoader('same_id'), equals(loader2));
      });

      test('should retrieve loader by ID', () {
        // Arrange
        final testItems = ['item1'];
        Widget itemBuilder(String item) => Text(item);

        final loader = loadingService.createLoader<String>(
          loaderId: 'retrievable_loader',
          items: testItems,
          itemBuilder: itemBuilder,
        );

        // Act
        final retrievedLoader = loadingService.getLoader('retrievable_loader');

        // Assert
        expect(retrievedLoader, equals(loader));
      });

      test('should return null for non-existent loader', () {
        // Act
        final loader = loadingService.getLoader('non_existent');

        // Assert
        expect(loader, isNull);
      });
    });

    group('Concurrent Load Management', () {
      test('should track concurrent loads correctly', () {
        // Arrange
        expect(loadingService.canStartNewLoad(), isTrue);

        // Act
        loadingService.registerLoad();
        loadingService.registerLoad();

        // Assert
        expect(loadingService.canStartNewLoad(),
            isTrue); // Should still allow more

        // Register maximum loads
        loadingService.registerLoad(); // Now at max (3)
        expect(loadingService.canStartNewLoad(), isFalse);
      });

      test('should unregister loads correctly', () {
        // Arrange
        loadingService.registerLoad();
        loadingService.registerLoad();
        loadingService.registerLoad(); // At maximum

        expect(loadingService.canStartNewLoad(), isFalse);

        // Act
        loadingService.unregisterLoad();

        // Assert
        expect(loadingService.canStartNewLoad(), isTrue);
      });

      test('should handle unregister when no loads are registered', () {
        // Act & Assert - Should not throw
        expect(() => loadingService.unregisterLoad(), returnsNormally);
      });
    });

    group('Service Statistics', () {
      test('should provide accurate statistics', () {
        // Arrange
        final testItems = ['item1', 'item2'];
        Widget itemBuilder(String item) => Text(item);

        loadingService.createLoader<String>(
          loaderId: 'stats_loader_1',
          items: testItems,
          itemBuilder: itemBuilder,
        );

        loadingService.createLoader<String>(
          loaderId: 'stats_loader_2',
          items: testItems,
          itemBuilder: itemBuilder,
        );

        loadingService.registerLoad();

        // Act
        final stats = loadingService.getStatistics();

        // Assert
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('active_loaders'), isTrue);
        expect(stats.containsKey('concurrent_loads'), isTrue);
        expect(stats.containsKey('max_concurrent_loads'), isTrue);
        expect(stats.containsKey('loader_ids'), isTrue);

        expect(stats['active_loaders'], equals(2));
        expect(stats['concurrent_loads'], equals(1));
        expect(stats['max_concurrent_loads'], equals(3));
        expect(stats['loader_ids'], contains('stats_loader_1'));
        expect(stats['loader_ids'], contains('stats_loader_2'));
      });
    });

    group('Resource Management', () {
      test('should dispose all loaders', () {
        // Arrange
        final testItems = ['item1'];
        Widget itemBuilder(String item) => Text(item);

        loadingService.createLoader<String>(
          loaderId: 'disposable_loader',
          items: testItems,
          itemBuilder: itemBuilder,
        );

        loadingService.registerLoad();

        // Act
        loadingService.dispose();

        // Assert
        final stats = loadingService.getStatistics();
        expect(stats['active_loaders'], equals(0));
        expect(stats['concurrent_loads'], equals(0));
      });
    });
  });

  group('ProgressiveLoader Tests', () {
    late ProgressiveLoader<String> loader;
    late List<String> testItems;
    late List<Widget> builtWidgets;

    setUp(() {
      testItems = List.generate(20, (i) => 'Item $i');
      builtWidgets = [];

      loader = ProgressiveLoader<String>(
        id: 'test_loader',
        items: testItems,
        itemBuilder: (item) {
          final widget = Text(item);
          builtWidgets.add(widget);
          return widget;
        },
        batchSize: 5,
        batchDelay: const Duration(milliseconds: 50),
        onDispose: () {},
      );
    });

    tearDown(() {
      loader.dispose();
    });

    group('Loader Properties', () {
      test('should have correct initial state', () {
        // Assert
        expect(loader.loadedItems, isEmpty);
        expect(loader.loadedWidgets, isEmpty);
        expect(loader.isLoading, isFalse);
        expect(loader.isCompleted, isFalse);
        expect(loader.progress, equals(0.0));
      });

      test('should calculate progress correctly', () {
        // Arrange - Create loader with known item count
        final smallLoader = ProgressiveLoader<String>(
          id: 'progress_test',
          items: ['a', 'b', 'c', 'd'],
          itemBuilder: (item) => Text(item),
          batchSize: 2,
          batchDelay: const Duration(milliseconds: 10),
        );

        // Assert initial progress
        expect(smallLoader.progress, equals(0.0));

        // Simulate partial loading by accessing private state
        // (In real implementation, this would happen through startLoading)
        expect(smallLoader.progress, equals(0.0));

        smallLoader.dispose();
      });

      test('should handle empty items list', () {
        // Arrange
        final emptyLoader = ProgressiveLoader<String>(
          id: 'empty_test',
          items: [],
          itemBuilder: (item) => Text(item),
          batchSize: 5,
          batchDelay: const Duration(milliseconds: 10),
        );

        // Assert
        expect(
            emptyLoader.progress, equals(1.0)); // 100% complete for empty list
        expect(emptyLoader.loadedItems, isEmpty);

        emptyLoader.dispose();
      });
    });

    group('Loading Process', () {
      test('should start loading process', () {
        // Act
        loader.startLoading();

        // Assert
        expect(loader.isLoading, isTrue);
        expect(loader.isCompleted, isFalse);
      });

      test('should not start loading if already loading', () {
        // Arrange
        loader.startLoading();
        expect(loader.isLoading, isTrue);

        // Act - Try to start again
        loader.startLoading();

        // Assert - Should still be in loading state
        expect(loader.isLoading, isTrue);
      });

      test('should not start loading if already completed', () async {
        // Arrange - Create a small loader that completes quickly
        final quickLoader = ProgressiveLoader<String>(
          id: 'quick_test',
          items: ['single_item'],
          itemBuilder: (item) => Text(item),
          batchSize: 10,
          batchDelay: const Duration(milliseconds: 1),
        );

        quickLoader.startLoading();

        // Wait for completion
        await Future.delayed(const Duration(milliseconds: 100));

        // Act - Try to start again
        quickLoader.startLoading();

        // Assert - Should remain completed
        expect(quickLoader.isCompleted, isTrue);

        quickLoader.dispose();
      });

      test('should load items in batches', () async {
        // Arrange
        final batchLoader = ProgressiveLoader<String>(
          id: 'batch_test',
          items: ['a', 'b', 'c', 'd', 'e', 'f'],
          itemBuilder: (item) => Text(item),
          batchSize: 2,
          batchDelay: const Duration(milliseconds: 10),
        );

        // Act
        batchLoader.startLoading();

        // Wait for first batch
        await Future.delayed(const Duration(milliseconds: 50));

        // Assert - Should have loaded some items but not all
        expect(batchLoader.loadedItems.length, greaterThan(0));
        expect(batchLoader.loadedItems.length, lessThanOrEqualTo(6));

        batchLoader.dispose();
      });
    });

    group('Loading Control', () {
      test('should pause loading', () {
        // Arrange
        loader.startLoading();
        expect(loader.isLoading, isTrue);

        // Act
        loader.pauseLoading();

        // Assert
        expect(loader.isLoading, isFalse);
        expect(loader.isCompleted, isFalse);
      });

      test('should resume loading', () {
        // Arrange
        loader.startLoading();
        loader.pauseLoading();
        expect(loader.isLoading, isFalse);

        // Act
        loader.resumeLoading();

        // Assert
        expect(loader.isLoading, isTrue);
      });

      test('should reset loader state', () async {
        // Arrange
        loader.startLoading();
        await Future.delayed(const Duration(milliseconds: 50));

        // Act
        loader.reset();

        // Assert
        expect(loader.loadedItems, isEmpty);
        expect(loader.loadedWidgets, isEmpty);
        expect(loader.isLoading, isFalse);
        expect(loader.isCompleted, isFalse);
        expect(loader.progress, equals(0.0));
      });
    });

    group('Error Handling', () {
      test('should handle item builder errors gracefully', () {
        // Arrange
        final errorLoader = ProgressiveLoader<String>(
          id: 'error_test',
          items: ['good', 'bad', 'good'],
          itemBuilder: (item) {
            if (item == 'bad') {
              throw Exception('Builder error');
            }
            return Text(item);
          },
          batchSize: 1,
          batchDelay: const Duration(milliseconds: 10),
        );

        // Act & Assert - Should not throw
        expect(() => errorLoader.startLoading(), returnsNormally);

        errorLoader.dispose();
      });
    });

    group('Performance Validation', () {
      test('should respect batch delay timing', () async {
        // Arrange
        final timedLoader = ProgressiveLoader<String>(
          id: 'timing_test',
          items: ['a', 'b', 'c'],
          itemBuilder: (item) => Text(item),
          batchSize: 1,
          batchDelay: const Duration(milliseconds: 100),
        );

        final stopwatch = Stopwatch()..start();

        // Act
        timedLoader.startLoading();

        // Wait for completion
        await Future.delayed(const Duration(milliseconds: 500));

        stopwatch.stop();

        // Assert - Should take at least the batch delay time
        expect(stopwatch.elapsedMilliseconds, greaterThan(100));

        timedLoader.dispose();
      });

      test('should not block main thread during loading', () async {
        // Arrange
        final heavyLoader = ProgressiveLoader<String>(
          id: 'heavy_test',
          items: List.generate(100, (i) => 'Heavy item $i'),
          itemBuilder: (item) => Text(item),
          batchSize: 10,
          batchDelay: const Duration(milliseconds: 1),
        );

        // Act
        heavyLoader.startLoading();

        // Simulate main thread work
        final mainThreadWork = Future(() {
          final stopwatch = Stopwatch()..start();
          for (int i = 0; i < 1000; i++) {
            final _ = DateTime.now().millisecondsSinceEpoch;
          }
          stopwatch.stop();
          return stopwatch.elapsedMilliseconds;
        });

        final mainThreadTime = await mainThreadWork;

        // Assert - Main thread should not be significantly blocked
        expect(mainThreadTime, lessThan(100));

        heavyLoader.dispose();
      });
    });

    group('Resource Management', () {
      test('should dispose properly', () {
        // Arrange
        loader.startLoading();

        // Act
        loader.dispose();

        // Assert - Should complete without throwing
        expect(true, isTrue);
      });

      test('should call onDispose callback', () {
        // Arrange
        bool disposeCalled = false;
        final callbackLoader = ProgressiveLoader<String>(
          id: 'callback_test',
          items: ['item'],
          itemBuilder: (item) => Text(item),
          batchSize: 1,
          batchDelay: const Duration(milliseconds: 1),
          onDispose: () => disposeCalled = true,
        );

        // Act
        callbackLoader.dispose();

        // Assert
        expect(disposeCalled, isTrue);
      });
    });

    group('Change Notification', () {
      test('should notify listeners on state changes', () {
        // Arrange
        bool notified = false;
        loader.addListener(() => notified = true);

        // Act
        loader.startLoading();

        // Assert
        expect(notified, isTrue);
      });

      test('should remove listeners properly', () {
        // Arrange
        bool notified = false;
        void listener() => notified = true;

        loader.addListener(listener);
        loader.removeListener(listener);

        // Act
        loader.startLoading();

        // Assert - Should not be notified after removal
        expect(notified, isFalse);
      });
    });
  });
}
