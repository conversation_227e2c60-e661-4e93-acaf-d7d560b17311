import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/services/cache_service.dart';

void main() {
  group('CacheService Tests', () {
    late CacheService cacheService;

    setUp(() {
      // Reset singleton for testing
      cacheService = CacheService();

      // Mock SharedPreferences.getInstance()
      SharedPreferences.setMockInitialValues({});
    });

    group('Data Storage and Retrieval', () {
      test('should save data successfully with compression', () async {
        // Arrange
        final testData = {'key': 'value', 'number': 42};
        const testKey = 'test_key';

        // Act
        final result = await cacheService.saveData(testKey, testData);

        // Assert
        expect(result, isTrue);

        // Verify data can be retrieved
        final retrievedData = await cacheService.getData(testKey);
        expect(retrievedData, equals(testData));
      });

      test('should return null for non-existent key', () async {
        // Act
        final result = await cacheService.getData('non_existent_key');

        // Assert
        expect(result, isNull);
      });

      test('should handle JSON serialization errors gracefully', () async {
        // Arrange - Create circular reference that can't be JSON encoded
        final Map<String, dynamic> circularData = {};
        circularData['self'] = circularData;

        // Act
        final result =
            await cacheService.saveData('circular_key', circularData);

        // Assert - Should handle error gracefully
        expect(result, isFalse);
      });
    });

    group('Memory Cache (LRU) Functionality', () {
      test('should use memory cache for fast retrieval', () async {
        // Arrange
        final testData = {'cached': 'data'};
        const testKey = 'memory_test';

        // Act - Save data first
        await cacheService.saveData(testKey, testData);

        // First retrieval should populate memory cache
        final firstRetrieval = await cacheService.getData(testKey);

        // Second retrieval should use memory cache (faster)
        final secondRetrieval = await cacheService.getData(testKey);

        // Assert
        expect(firstRetrieval, equals(testData));
        expect(secondRetrieval, equals(testData));

        // Verify cache statistics show hits
        final stats = cacheService.getCacheStatistics();
        expect(stats['cache_hits'], greaterThan(0));
      });

      test('should track cache hits and misses correctly', () async {
        // Arrange
        const existingKey = 'existing';
        const nonExistentKey = 'non_existent';
        final testData = {'test': 'data'};

        // Act
        await cacheService.saveData(existingKey, testData);

        // Hit: retrieve existing data
        await cacheService.getData(existingKey);

        // Miss: try to retrieve non-existent data
        await cacheService.getData(nonExistentKey);

        // Assert
        final stats = cacheService.getCacheStatistics();
        expect(stats['cache_hits'], greaterThan(0));
        expect(stats['cache_misses'], greaterThan(0));
        expect(stats['hit_rate'], greaterThan(0.0));
      });
    });

    group('Cache Expiration', () {
      test('should respect cache expiration when checking', () async {
        // This test verifies the expiration logic without actually waiting
        // We test the logic by manipulating timestamps

        // Arrange
        const testKey = 'expiration_test';
        final testData = {'data': 'expires'};

        // Act
        await cacheService.saveData(testKey, testData);

        // Immediately retrieve (should not be expired)
        final freshData =
            await cacheService.getData(testKey, checkExpiration: true);
        expect(freshData, equals(testData));

        // Retrieve without expiration check (should always work)
        final uncheckedData =
            await cacheService.getData(testKey, checkExpiration: false);
        expect(uncheckedData, equals(testData));
      });
    });

    group('Cache Compression', () {
      test('should compress and decompress data correctly', () async {
        // Arrange - Large data that benefits from compression
        final largeData = {
          'description': 'A' * 1000, // Large string
          'items': List.generate(100, (i) => {'id': i, 'name': 'Item $i'}),
          'metadata': {
            'created': DateTime.now().toIso8601String(),
            'version': '1.0.0',
            'tags': List.generate(50, (i) => 'tag$i'),
          }
        };
        const testKey = 'compression_test';

        // Act
        final saveResult = await cacheService.saveData(testKey, largeData);
        final retrievedData = await cacheService.getData(testKey);

        // Assert
        expect(saveResult, isTrue);
        expect(retrievedData, equals(largeData));

        // Verify compression statistics
        final stats = cacheService.getCacheStatistics();
        expect(stats['total_cache_size'], greaterThan(0));
      });
    });

    group('Cache Statistics', () {
      test('should provide accurate cache statistics', () async {
        // Arrange
        final testData1 = {'key1': 'value1'};
        final testData2 = {'key2': 'value2'};

        // Act
        await cacheService.saveData('key1', testData1);
        await cacheService.saveData('key2', testData2);

        // Generate some hits and misses
        await cacheService.getData('key1'); // Hit
        await cacheService.getData('key2'); // Hit
        await cacheService.getData('nonexistent'); // Miss

        final stats = cacheService.getCacheStatistics();

        // Assert
        expect(stats, isA<Map<String, dynamic>>());
        expect(stats.containsKey('cache_hits'), isTrue);
        expect(stats.containsKey('cache_misses'), isTrue);
        expect(stats.containsKey('hit_rate'), isTrue);
        expect(stats.containsKey('total_cache_size'), isTrue);
        expect(stats.containsKey('memory_cache_size'), isTrue);
        expect(stats.containsKey('disk_cache_tracked'), isTrue);

        expect(stats['cache_hits'], greaterThan(0));
        expect(stats['cache_misses'], greaterThan(0));
        expect(stats['hit_rate'], greaterThan(0.0));
        expect(stats['hit_rate'], lessThanOrEqualTo(1.0));
      });
    });

    group('Intelligent Cleanup', () {
      test('should perform intelligent cleanup without errors', () async {
        // Arrange - Add some test data
        for (int i = 0; i < 10; i++) {
          await cacheService.saveData('test_key_$i', {'data': 'value_$i'});
        }

        // Act
        await cacheService.performIntelligentCleanup();

        // Assert - Should complete without throwing
        // The cleanup logic is designed to be safe and non-destructive
        expect(true, isTrue); // Test passes if no exception is thrown
      });
    });

    group('Cache Key Management', () {
      test('should provide correct cache keys', () {
        // Act & Assert
        expect(cacheService.bookingsKey, equals('cached_bookings'));
        expect(cacheService.experiencesKey, equals('cached_experiences'));
        expect(cacheService.reviewsKey, equals('cached_reviews'));
      });
    });

    group('Error Handling', () {
      test('should handle cache clear errors gracefully', () async {
        // Act
        final result = await cacheService.clearCache('any_key');

        // Assert - Should not throw, returns boolean result
        expect(result, isA<bool>());
      });
    });

    group('Performance Validation', () {
      test('should maintain performance targets', () async {
        // This test validates that cache operations are performant
        final stopwatch = Stopwatch()..start();

        // Perform multiple cache operations
        for (int i = 0; i < 50; i++) {
          await cacheService.saveData('perf_test_$i', {'index': i});
        }

        for (int i = 0; i < 50; i++) {
          await cacheService.getData('perf_test_$i');
        }

        stopwatch.stop();

        // Assert - Operations should complete within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(5000)); // 5 seconds max

        // Verify cache hit rate is good
        final stats = cacheService.getCacheStatistics();
        expect(stats['hit_rate'], greaterThan(0.5)); // At least 50% hit rate
      });
    });
  });
}
