import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

/// Type of AR content
enum ARContentType {
  /// 3D model
  model,

  /// Image overlay
  image,

  /// Video overlay
  video,

  /// Audio overlay
  audio,

  /// Text overlay
  text,

  /// Interactive experience
  interactive,
}

/// Extension for AR content type
extension ARContentTypeExtension on ARContentType {
  /// Get the display name for the AR content type
  String get displayName {
    switch (this) {
      case ARContentType.model:
        return '3D Model';
      case ARContentType.image:
        return 'Image Overlay';
      case ARContentType.video:
        return 'Video Overlay';
      case ARContentType.audio:
        return 'Audio Guide';
      case ARContentType.text:
        return 'Text Information';
      case ARContentType.interactive:
        return 'Interactive Experience';
    }
  }

  /// Get the icon for the AR content type
  IconData get icon {
    switch (this) {
      case ARContentType.model:
        return Icons.view_in_ar;
      case ARContentType.image:
        return Icons.image;
      case ARContentType.video:
        return Icons.videocam;
      case ARContentType.audio:
        return Icons.headset;
      case ARContentType.text:
        return Icons.text_fields;
      case ARContentType.interactive:
        return Icons.touch_app;
    }
  }

  /// Get the color for the AR content type
  Color get color {
    switch (this) {
      case ARContentType.model:
        return Colors.blue;
      case ARContentType.image:
        return Colors.green;
      case ARContentType.video:
        return Colors.red;
      case ARContentType.audio:
        return Colors.purple;
      case ARContentType.text:
        return Colors.orange;
      case ARContentType.interactive:
        return Colors.teal;
    }
  }
}

/// A marker for AR content
class ARContentMarker {
  /// The unique identifier
  final String id;

  /// The title
  final String title;

  /// The description
  final String? description;

  /// The type of AR content
  final ARContentType contentType;

  /// The URL to the AR content
  final String contentUrl;

  /// The thumbnail URL
  final String? thumbnailUrl;

  /// The size of the content in bytes
  final int? contentSize;

  /// The duration of the content in seconds (for video/audio)
  final int? durationSeconds;

  /// The location name
  final String? location;

  /// The coordinates (latitude and longitude)
  final Map<String, double>? coordinates;

  /// Whether the content is available offline
  final bool isAvailableOffline;

  /// The created at timestamp
  final DateTime createdAt;

  /// The updated at timestamp
  final DateTime updatedAt;

  /// Creates a new AR content marker
  ARContentMarker({
    String? id,
    required this.title,
    this.description,
    required this.contentType,
    required this.contentUrl,
    this.thumbnailUrl,
    this.contentSize,
    this.durationSeconds,
    this.location,
    this.coordinates,
    this.isAvailableOffline = false,
    DateTime? createdAt,
    DateTime? updatedAt,
  })  : id = id ?? const Uuid().v4(),
        createdAt = createdAt ?? DateTime.now(),
        updatedAt = updatedAt ?? DateTime.now();

  /// Get the formatted content size
  String? get formattedSize {
    if (contentSize == null) return null;

    if (contentSize! < 1024) {
      return '$contentSize B';
    } else if (contentSize! < 1024 * 1024) {
      final kb = contentSize! / 1024;
      return '${kb.toStringAsFixed(1)} KB';
    } else if (contentSize! < 1024 * 1024 * 1024) {
      final mb = contentSize! / (1024 * 1024);
      return '${mb.toStringAsFixed(1)} MB';
    } else {
      final gb = contentSize! / (1024 * 1024 * 1024);
      return '${gb.toStringAsFixed(1)} GB';
    }
  }

  /// Get the formatted duration
  String? get formattedDuration {
    if (durationSeconds == null) return null;

    final minutes = durationSeconds! ~/ 60;
    final seconds = durationSeconds! % 60;

    return '$minutes:${seconds.toString().padLeft(2, '0')}';
  }

  /// Create a copy of this marker with the given fields replaced
  ARContentMarker copyWith({
    String? id,
    String? title,
    String? description,
    ARContentType? contentType,
    String? contentUrl,
    String? thumbnailUrl,
    int? contentSize,
    int? durationSeconds,
    String? location,
    Map<String, double>? coordinates,
    bool? isAvailableOffline,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return ARContentMarker(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      contentType: contentType ?? this.contentType,
      contentUrl: contentUrl ?? this.contentUrl,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      contentSize: contentSize ?? this.contentSize,
      durationSeconds: durationSeconds ?? this.durationSeconds,
      location: location ?? this.location,
      coordinates: coordinates ?? this.coordinates,
      isAvailableOffline: isAvailableOffline ?? this.isAvailableOffline,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  /// Create an AR content marker from JSON
  factory ARContentMarker.fromJson(Map<String, dynamic> json) {
    return ARContentMarker(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      contentType: _parseARContentType(json['contentType']),
      contentUrl: json['contentUrl'],
      thumbnailUrl: json['thumbnailUrl'],
      contentSize: json['contentSize'],
      durationSeconds: json['durationSeconds'],
      location: json['location'],
      coordinates: json['coordinates'] != null
          ? Map<String, double>.from(json['coordinates'])
          : null,
      isAvailableOffline: json['isAvailableOffline'] ?? false,
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: DateTime.parse(json['updatedAt']),
    );
  }

  /// Parse AR content type from string
  static ARContentType _parseARContentType(String? contentType) {
    if (contentType == null) return ARContentType.model;

    switch (contentType) {
      case 'model':
        return ARContentType.model;
      case 'image':
        return ARContentType.image;
      case 'video':
        return ARContentType.video;
      case 'audio':
        return ARContentType.audio;
      case 'text':
        return ARContentType.text;
      case 'interactive':
        return ARContentType.interactive;
      default:
        return ARContentType.model;
    }
  }

  /// Convert this AR content marker to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'contentType': contentType.name,
      'contentUrl': contentUrl,
      'thumbnailUrl': thumbnailUrl,
      'contentSize': contentSize,
      'durationSeconds': durationSeconds,
      'location': location,
      'coordinates': coordinates,
      'isAvailableOffline': isAvailableOffline,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }
}
