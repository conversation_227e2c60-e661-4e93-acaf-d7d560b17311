import 'package:flutter/foundation.dart';

/// Health check types for categorization
enum HealthCheckType {
  comprehensive,
  connectivity,
  storage,
  cache,
  service,
  performance,
  memory,
  battery,
}

extension HealthCheckTypeExtension on HealthCheckType {
  String get displayName {
    switch (this) {
      case HealthCheckType.comprehensive:
        return 'Comprehensive Check';
      case HealthCheckType.connectivity:
        return 'Connectivity Check';
      case HealthCheckType.storage:
        return 'Storage Check';
      case HealthCheckType.cache:
        return 'Cache Check';
      case HealthCheckType.service:
        return 'Service Check';
      case HealthCheckType.performance:
        return 'Performance Check';
      case HealthCheckType.memory:
        return 'Memory Check';
      case HealthCheckType.battery:
        return 'Battery Check';
    }
  }

  String get value {
    switch (this) {
      case HealthCheckType.comprehensive:
        return 'comprehensive';
      case HealthCheckType.connectivity:
        return 'connectivity';
      case HealthCheckType.storage:
        return 'storage';
      case HealthCheckType.cache:
        return 'cache';
      case HealthCheckType.service:
        return 'service';
      case HealthCheckType.performance:
        return 'performance';
      case HealthCheckType.memory:
        return 'memory';
      case HealthCheckType.battery:
        return 'battery';
    }
  }
}

/// Health status levels
enum HealthStatus {
  healthy,
  warning,
  critical,
  unknown,
}

extension HealthStatusExtension on HealthStatus {
  String get displayName {
    switch (this) {
      case HealthStatus.healthy:
        return 'Healthy';
      case HealthStatus.warning:
        return 'Warning';
      case HealthStatus.critical:
        return 'Critical';
      case HealthStatus.unknown:
        return 'Unknown';
    }
  }

  String get value {
    switch (this) {
      case HealthStatus.healthy:
        return 'healthy';
      case HealthStatus.warning:
        return 'warning';
      case HealthStatus.critical:
        return 'critical';
      case HealthStatus.unknown:
        return 'unknown';
    }
  }
}

/// Model representing a health check result
@immutable
class HealthCheck {
  /// Unique identifier for the health check
  final String id;

  /// Timestamp when the health check was performed
  final DateTime timestamp;

  /// Type of health check
  final HealthCheckType type;

  /// Health status result
  final HealthStatus status;

  /// Session ID for correlation
  final String sessionId;

  /// Duration of the health check
  final Duration? duration;

  /// Additional details about the health check
  final Map<String, dynamic> details;

  /// Error message if the check failed
  final String? errorMessage;

  /// Recommendations based on the health check
  final List<String> recommendations;

  /// Threshold values that were checked
  final Map<String, double> thresholds;

  /// Actual values measured
  final Map<String, double> measurements;

  const HealthCheck({
    required this.id,
    required this.timestamp,
    required this.type,
    this.status = HealthStatus.unknown,
    required this.sessionId,
    this.duration,
    this.details = const {},
    this.errorMessage,
    this.recommendations = const [],
    this.thresholds = const {},
    this.measurements = const {},
  });

  /// Create a copy with updated values
  HealthCheck copyWith({
    String? id,
    DateTime? timestamp,
    HealthCheckType? type,
    HealthStatus? status,
    String? sessionId,
    Duration? duration,
    Map<String, dynamic>? details,
    String? errorMessage,
    List<String>? recommendations,
    Map<String, double>? thresholds,
    Map<String, double>? measurements,
  }) {
    return HealthCheck(
      id: id ?? this.id,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
      status: status ?? this.status,
      sessionId: sessionId ?? this.sessionId,
      duration: duration ?? this.duration,
      details: details ?? this.details,
      errorMessage: errorMessage ?? this.errorMessage,
      recommendations: recommendations ?? this.recommendations,
      thresholds: thresholds ?? this.thresholds,
      measurements: measurements ?? this.measurements,
    );
  }

  /// Check if the health check passed
  bool get isPassing => status == HealthStatus.healthy;

  /// Check if the health check has warnings
  bool get hasWarnings => status == HealthStatus.warning;

  /// Check if the health check is critical
  bool get isCritical => status == HealthStatus.critical;

  /// Get health score (0-100)
  int get healthScore {
    switch (status) {
      case HealthStatus.healthy:
        return 100;
      case HealthStatus.warning:
        return 60;
      case HealthStatus.critical:
        return 20;
      case HealthStatus.unknown:
        return 0;
    }
  }

  /// Convert to JSON for persistence
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'timestamp': timestamp.toIso8601String(),
      'type': type.value,
      'status': status.value,
      'sessionId': sessionId,
      'duration': duration?.inMilliseconds,
      'details': details,
      'errorMessage': errorMessage,
      'recommendations': recommendations,
      'thresholds': thresholds,
      'measurements': measurements,
    };
  }

  /// Create from JSON
  factory HealthCheck.fromJson(Map<String, dynamic> json) {
    return HealthCheck(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      type: HealthCheckType.values.firstWhere(
        (t) => t.value == json['type'],
        orElse: () => HealthCheckType.comprehensive,
      ),
      status: HealthStatus.values.firstWhere(
        (s) => s.value == json['status'],
        orElse: () => HealthStatus.unknown,
      ),
      sessionId: json['sessionId'] as String,
      duration: json['duration'] != null
          ? Duration(milliseconds: json['duration'] as int)
          : null,
      details: Map<String, dynamic>.from(json['details'] as Map? ?? {}),
      errorMessage: json['errorMessage'] as String?,
      recommendations:
          List<String>.from(json['recommendations'] as List? ?? []),
      thresholds: Map<String, double>.from(
        (json['thresholds'] as Map? ?? {}).map(
          (key, value) => MapEntry(key.toString(), (value as num).toDouble()),
        ),
      ),
      measurements: Map<String, double>.from(
        (json['measurements'] as Map? ?? {}).map(
          (key, value) => MapEntry(key.toString(), (value as num).toDouble()),
        ),
      ),
    );
  }

  @override
  String toString() {
    return 'HealthCheck(id: $id, type: ${type.displayName}, '
        'status: ${status.displayName}, timestamp: $timestamp)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is HealthCheck &&
        other.id == id &&
        other.timestamp == timestamp &&
        other.type == type &&
        other.status == status &&
        other.sessionId == sessionId;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      timestamp,
      type,
      status,
      sessionId,
    );
  }
}

/// Model representing connectivity health check
@immutable
class ConnectivityCheck extends HealthCheck {
  /// Network type (wifi, mobile, etc.)
  final String networkType;

  /// Connection speed in Mbps
  final double? connectionSpeed;

  /// Latency in milliseconds
  final int? latency;

  /// Whether internet is reachable
  final bool isInternetReachable;

  const ConnectivityCheck({
    required super.id,
    required super.timestamp,
    required super.sessionId,
    super.status = HealthStatus.unknown,
    super.duration,
    super.details = const {},
    super.errorMessage,
    super.recommendations = const [],
    required this.networkType,
    this.connectionSpeed,
    this.latency,
    this.isInternetReachable = false,
  }) : super(type: HealthCheckType.connectivity);

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'networkType': networkType,
      'connectionSpeed': connectionSpeed,
      'latency': latency,
      'isInternetReachable': isInternetReachable,
    });
    return json;
  }

  factory ConnectivityCheck.fromJson(Map<String, dynamic> json) {
    return ConnectivityCheck(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      sessionId: json['sessionId'] as String,
      status: HealthStatus.values.firstWhere(
        (s) => s.value == json['status'],
        orElse: () => HealthStatus.unknown,
      ),
      duration: json['duration'] != null
          ? Duration(milliseconds: json['duration'] as int)
          : null,
      details: Map<String, dynamic>.from(json['details'] as Map? ?? {}),
      errorMessage: json['errorMessage'] as String?,
      recommendations:
          List<String>.from(json['recommendations'] as List? ?? []),
      networkType: json['networkType'] as String,
      connectionSpeed: (json['connectionSpeed'] as num?)?.toDouble(),
      latency: json['latency'] as int?,
      isInternetReachable: json['isInternetReachable'] as bool? ?? false,
    );
  }
}

/// Model representing storage health check
@immutable
class StorageCheck extends HealthCheck {
  /// Total storage space in bytes
  final int totalSpace;

  /// Available storage space in bytes
  final int availableSpace;

  /// Used storage space in bytes
  final int usedSpace;

  /// Storage usage percentage
  final double usagePercentage;

  const StorageCheck({
    required super.id,
    required super.timestamp,
    required super.sessionId,
    super.status = HealthStatus.unknown,
    super.duration,
    super.details = const {},
    super.errorMessage,
    super.recommendations = const [],
    required this.totalSpace,
    required this.availableSpace,
    required this.usedSpace,
    required this.usagePercentage,
  }) : super(type: HealthCheckType.storage);

  @override
  Map<String, dynamic> toJson() {
    final json = super.toJson();
    json.addAll({
      'totalSpace': totalSpace,
      'availableSpace': availableSpace,
      'usedSpace': usedSpace,
      'usagePercentage': usagePercentage,
    });
    return json;
  }

  factory StorageCheck.fromJson(Map<String, dynamic> json) {
    return StorageCheck(
      id: json['id'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      sessionId: json['sessionId'] as String,
      status: HealthStatus.values.firstWhere(
        (s) => s.value == json['status'],
        orElse: () => HealthStatus.unknown,
      ),
      duration: json['duration'] != null
          ? Duration(milliseconds: json['duration'] as int)
          : null,
      details: Map<String, dynamic>.from(json['details'] as Map? ?? {}),
      errorMessage: json['errorMessage'] as String?,
      recommendations:
          List<String>.from(json['recommendations'] as List? ?? []),
      totalSpace: json['totalSpace'] as int,
      availableSpace: json['availableSpace'] as int,
      usedSpace: json['usedSpace'] as int,
      usagePercentage: (json['usagePercentage'] as num).toDouble(),
    );
  }
}
