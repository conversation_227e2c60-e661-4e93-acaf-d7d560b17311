import 'package:equatable/equatable.dart';

/// Enum for supported payment providers
enum PaymentProvider {
  stripe,
  paystack,
  busha,
}

/// Enum for payment method types
enum PaymentMethodType {
  card,
  bankTransfer,
  ussd,
  mobileMoney,
  crypto,
}

/// Enum for payment status
enum PaymentStatus {
  pending,
  processing,
  successful,
  failed,
  cancelled,
  expired,
}

/// Request model for payment initialization
class PaymentInitRequest extends Equatable {
  final String bookingId;
  final double amount;
  final String currency;
  final String userEmail;
  final String userName;
  final String? userPhone;
  final String? countryCode;
  final double? latitude;
  final double? longitude;
  final Map<String, dynamic>? metadata;

  const PaymentInitRequest({
    required this.bookingId,
    required this.amount,
    required this.currency,
    required this.userEmail,
    required this.userName,
    this.userPhone,
    this.countryCode,
    this.latitude,
    this.longitude,
    this.metadata,
  });

  Map<String, dynamic> toJson() {
    return {
      'booking_id': bookingId,
      'amount': amount,
      'currency': currency,
      'user_email': userEmail,
      'user_name': userName,
      if (userPhone != null) 'user_phone': userPhone,
      if (countryCode != null) 'country_code': countryCode,
      if (latitude != null) 'latitude': latitude,
      if (longitude != null) 'longitude': longitude,
      if (metadata != null) 'metadata': metadata,
    };
  }

  @override
  List<Object?> get props => [
        bookingId,
        amount,
        currency,
        userEmail,
        userName,
        userPhone,
        countryCode,
        latitude,
        longitude,
        metadata,
      ];
}

/// Response model for payment initialization
class PaymentInitResponse extends Equatable {
  final String transactionReference;
  final PaymentProvider selectedProvider;
  final PaymentProviderConfig providerConfig;
  final GeolocationData? geolocationData;
  final List<PaymentMethodType> availablePaymentMethods;
  final String correlationId;

  const PaymentInitResponse({
    required this.transactionReference,
    required this.selectedProvider,
    required this.providerConfig,
    this.geolocationData,
    required this.availablePaymentMethods,
    required this.correlationId,
  });

  factory PaymentInitResponse.fromJson(Map<String, dynamic> json) {
    return PaymentInitResponse(
      transactionReference: json['transaction_reference'] as String,
      selectedProvider: PaymentProvider.values.firstWhere(
        (p) => p.name == json['selected_provider'],
      ),
      providerConfig: PaymentProviderConfig.fromJson(
        json['provider_config'] as Map<String, dynamic>,
      ),
      geolocationData: json['geolocation_data'] != null
          ? GeolocationData.fromJson(
              json['geolocation_data'] as Map<String, dynamic>,
            )
          : null,
      availablePaymentMethods: (json['available_payment_methods'] as List)
          .map((method) => PaymentMethodType.values.firstWhere(
                (m) => m.name == method,
              ))
          .toList(),
      correlationId: json['correlation_id'] as String,
    );
  }

  @override
  List<Object?> get props => [
        transactionReference,
        selectedProvider,
        providerConfig,
        geolocationData,
        availablePaymentMethods,
        correlationId,
      ];
}

/// Request model for payment verification
class PaymentVerificationRequest extends Equatable {
  final String transactionReference;
  final String? providerTransactionId;
  final String correlationId;

  const PaymentVerificationRequest({
    required this.transactionReference,
    this.providerTransactionId,
    required this.correlationId,
  });

  Map<String, dynamic> toJson() {
    return {
      'transaction_reference': transactionReference,
      if (providerTransactionId != null)
        'provider_transaction_id': providerTransactionId,
      'correlation_id': correlationId,
    };
  }

  @override
  List<Object?> get props => [
        transactionReference,
        providerTransactionId,
        correlationId,
      ];
}

/// Response model for payment verification
class PaymentVerificationResponse extends Equatable {
  final String transactionReference;
  final PaymentStatus status;
  final double? amountPaid;
  final String? currency;
  final String? providerTransactionId;
  final String? receiptId;
  final String? failureReason;
  final DateTime? completedAt;
  final Map<String, dynamic>? metadata;

  const PaymentVerificationResponse({
    required this.transactionReference,
    required this.status,
    this.amountPaid,
    this.currency,
    this.providerTransactionId,
    this.receiptId,
    this.failureReason,
    this.completedAt,
    this.metadata,
  });

  factory PaymentVerificationResponse.fromJson(Map<String, dynamic> json) {
    return PaymentVerificationResponse(
      transactionReference: json['transaction_reference'] as String,
      status: PaymentStatus.values.firstWhere(
        (s) => s.name == json['status'],
      ),
      amountPaid: json['amount_paid']?.toDouble(),
      currency: json['currency'] as String?,
      providerTransactionId: json['provider_transaction_id'] as String?,
      receiptId: json['receipt_id'] as String?,
      failureReason: json['failure_reason'] as String?,
      completedAt: json['completed_at'] != null
          ? DateTime.parse(json['completed_at'] as String)
          : null,
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  bool get isSuccessful => status == PaymentStatus.successful;
  bool get isFailed => status == PaymentStatus.failed;
  bool get isPending => status == PaymentStatus.pending;

  @override
  List<Object?> get props => [
        transactionReference,
        status,
        amountPaid,
        currency,
        providerTransactionId,
        receiptId,
        failureReason,
        completedAt,
        metadata,
      ];
}

/// Geolocation data for payment routing
class GeolocationData extends Equatable {
  final String countryCode;
  final String countryName;
  final String region;
  final String city;
  final double latitude;
  final double longitude;
  final double confidence;
  final bool isVpnDetected;
  final String recommendedProvider;

  const GeolocationData({
    required this.countryCode,
    required this.countryName,
    required this.region,
    required this.city,
    required this.latitude,
    required this.longitude,
    required this.confidence,
    required this.isVpnDetected,
    required this.recommendedProvider,
  });

  factory GeolocationData.fromJson(Map<String, dynamic> json) {
    return GeolocationData(
      countryCode: json['country_code'] as String,
      countryName: json['country_name'] as String,
      region: json['region'] as String,
      city: json['city'] as String,
      latitude: json['latitude'].toDouble(),
      longitude: json['longitude'].toDouble(),
      confidence: json['confidence'].toDouble(),
      isVpnDetected: json['is_vpn_detected'] as bool,
      recommendedProvider: json['recommended_provider'] as String,
    );
  }

  bool get isAfricanRegion => region.toLowerCase().contains('africa');
  bool get isDiasporaRegion => !isAfricanRegion;

  @override
  List<Object?> get props => [
        countryCode,
        countryName,
        region,
        city,
        latitude,
        longitude,
        confidence,
        isVpnDetected,
        recommendedProvider,
      ];
}

/// Provider-specific configuration
class PaymentProviderConfig extends Equatable {
  final PaymentProvider provider;
  final String? clientSecret; // For Stripe
  final String? authorizationUrl; // For Paystack
  final String? publicKey; // For Paystack
  final String? walletAddress; // For Busha
  final String? qrCodeData; // For Busha
  final double? cryptoAmount; // For Busha
  final String? cryptoCurrency; // For Busha
  final int? expirationMinutes; // For Busha
  final Map<String, dynamic>? additionalConfig;

  const PaymentProviderConfig({
    required this.provider,
    this.clientSecret,
    this.authorizationUrl,
    this.publicKey,
    this.walletAddress,
    this.qrCodeData,
    this.cryptoAmount,
    this.cryptoCurrency,
    this.expirationMinutes,
    this.additionalConfig,
  });

  factory PaymentProviderConfig.fromJson(Map<String, dynamic> json) {
    return PaymentProviderConfig(
      provider: PaymentProvider.values.firstWhere(
        (p) => p.name == json['provider'],
      ),
      clientSecret: json['client_secret'] as String?,
      authorizationUrl: json['authorization_url'] as String?,
      publicKey: json['public_key'] as String?,
      walletAddress: json['wallet_address'] as String?,
      qrCodeData: json['qr_code_data'] as String?,
      cryptoAmount: json['crypto_amount']?.toDouble(),
      cryptoCurrency: json['crypto_currency'] as String?,
      expirationMinutes: json['expiration_minutes'] as int?,
      additionalConfig: json['additional_config'] as Map<String, dynamic>?,
    );
  }

  @override
  List<Object?> get props => [
        provider,
        clientSecret,
        authorizationUrl,
        publicKey,
        walletAddress,
        qrCodeData,
        cryptoAmount,
        cryptoCurrency,
        expirationMinutes,
        additionalConfig,
      ];
}
