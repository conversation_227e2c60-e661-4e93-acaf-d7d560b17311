/// A model representing an exchange rate between two currencies
class ExchangeRateModel {
  /// The base currency code (e.g., USD)
  final String baseCurrency;

  /// The target currency code (e.g., EUR)
  final String targetCurrency;

  /// The exchange rate (1 unit of base currency = rate units of target currency)
  final double rate;

  /// When the exchange rate was last updated
  final DateTime timestamp;

  /// Whether this rate is from cache
  final bool isFromCache;

  /// The source of the exchange rate data
  final String source;

  /// Creates a new exchange rate model
  const ExchangeRateModel({
    required this.baseCurrency,
    required this.targetCurrency,
    required this.rate,
    required this.timestamp,
    this.isFromCache = false,
    this.source = 'API',
  });

  /// Creates a copy with some fields replaced
  ExchangeRateModel copyWith({
    String? baseCurrency,
    String? targetCurrency,
    double? rate,
    DateTime? timestamp,
    bool? isFromCache,
    String? source,
  }) {
    return ExchangeRateModel(
      baseCurrency: baseCurrency ?? this.baseCurrency,
      targetCurrency: targetCurrency ?? this.targetCurrency,
      rate: rate ?? this.rate,
      timestamp: timestamp ?? this.timestamp,
      isFromCache: isFromCache ?? this.isFromCache,
      source: source ?? this.source,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'baseCurrency': baseCurrency,
      'targetCurrency': targetCurrency,
      'rate': rate,
      'timestamp': timestamp.toIso8601String(),
      'isFromCache': isFromCache,
      'source': source,
    };
  }

  /// Creates from JSON
  factory ExchangeRateModel.fromJson(Map<String, dynamic> json) {
    return ExchangeRateModel(
      baseCurrency: json['baseCurrency'] as String,
      targetCurrency: json['targetCurrency'] as String,
      rate: json['rate'] as double,
      timestamp: DateTime.parse(json['timestamp'] as String),
      isFromCache: json['isFromCache'] as bool? ?? false,
      source: json['source'] as String? ?? 'API',
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is ExchangeRateModel &&
        other.baseCurrency == baseCurrency &&
        other.targetCurrency == targetCurrency &&
        other.rate == rate &&
        other.timestamp == timestamp &&
        other.isFromCache == isFromCache &&
        other.source == source;
  }

  @override
  int get hashCode {
    return baseCurrency.hashCode ^
        targetCurrency.hashCode ^
        rate.hashCode ^
        timestamp.hashCode ^
        isFromCache.hashCode ^
        source.hashCode;
  }

  /// Convert an amount from the base currency to the target currency
  double convert(double amount) {
    return amount * rate;
  }

  /// Convert an amount from the target currency to the base currency
  double convertReverse(double amount) {
    return amount / rate;
  }

  /// Get the age of this exchange rate in minutes
  int get ageInMinutes {
    return DateTime.now().difference(timestamp).inMinutes;
  }

  /// Check if this exchange rate is considered fresh (less than 60 minutes old)
  bool get isFresh {
    return ageInMinutes < 60;
  }

  /// Get a string representation of when this rate was last updated
  String get lastUpdated {
    final minutes = ageInMinutes;
    if (minutes < 1) {
      return 'Just now';
    } else if (minutes < 60) {
      return '$minutes ${minutes == 1 ? 'minute' : 'minutes'} ago';
    } else if (minutes < 1440) {
      final hours = (minutes / 60).floor();
      return '$hours ${hours == 1 ? 'hour' : 'hours'} ago';
    } else {
      final days = (minutes / 1440).floor();
      return '$days ${days == 1 ? 'day' : 'days'} ago';
    }
  }
}

/// A model representing a collection of exchange rates
class ExchangeRatesCollection {
  /// The base currency for all rates
  final String baseCurrency;

  /// The timestamp when these rates were last updated
  final DateTime timestamp;

  /// The exchange rates, keyed by target currency code
  final Map<String, double> rates;

  /// Whether these rates are from cache
  final bool isFromCache;

  /// The source of the exchange rate data
  final String source;

  /// Creates a new exchange rates collection
  const ExchangeRatesCollection({
    required this.baseCurrency,
    required this.timestamp,
    required this.rates,
    this.isFromCache = false,
    this.source = 'API',
  });

  /// Creates a copy with some fields replaced
  ExchangeRatesCollection copyWith({
    String? baseCurrency,
    DateTime? timestamp,
    Map<String, double>? rates,
    bool? isFromCache,
    String? source,
  }) {
    return ExchangeRatesCollection(
      baseCurrency: baseCurrency ?? this.baseCurrency,
      timestamp: timestamp ?? this.timestamp,
      rates: rates ?? this.rates,
      isFromCache: isFromCache ?? this.isFromCache,
      source: source ?? this.source,
    );
  }

  /// Converts to JSON
  Map<String, dynamic> toJson() {
    return {
      'baseCurrency': baseCurrency,
      'timestamp': timestamp.toIso8601String(),
      'rates': rates,
      'isFromCache': isFromCache,
      'source': source,
    };
  }

  /// Creates from JSON
  factory ExchangeRatesCollection.fromJson(Map<String, dynamic> json) {
    return ExchangeRatesCollection(
      baseCurrency: json['baseCurrency'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
      rates: Map<String, double>.from(json['rates'] as Map),
      isFromCache: json['isFromCache'] as bool? ?? false,
      source: json['source'] as String? ?? 'API',
    );
  }

  /// Get the exchange rate for a specific target currency
  ExchangeRateModel getRate(String targetCurrency) {
    if (!rates.containsKey(targetCurrency)) {
      throw Exception('Exchange rate not found for $targetCurrency');
    }

    return ExchangeRateModel(
      baseCurrency: baseCurrency,
      targetCurrency: targetCurrency,
      rate: rates[targetCurrency]!,
      timestamp: timestamp,
      isFromCache: isFromCache,
      source: source,
    );
  }

  /// Convert an amount from the base currency to a target currency
  double convert(double amount, String targetCurrency) {
    if (!rates.containsKey(targetCurrency)) {
      throw Exception('Exchange rate not found for $targetCurrency');
    }

    return amount * rates[targetCurrency]!;
  }

  /// Get the age of these exchange rates in minutes
  int get ageInMinutes {
    return DateTime.now().difference(timestamp).inMinutes;
  }

  /// Check if these exchange rates are considered fresh (less than 60 minutes old)
  bool get isFresh {
    return ageInMinutes < 60;
  }
}
