// Flutter imports
import 'package:flutter/material.dart';

// Package imports
import 'package:uuid/uuid.dart';

/// Enum representing different mood types with emoji representations
enum MoodType {
  /// Very happy mood (😄) - Excellent experience
  veryHappy,

  /// Happy mood (😊) - Good experience
  happy,

  /// Neutral mood (😐) - Average experience
  neutral,

  /// Sad mood (😔) - Poor experience
  sad,

  /// Very sad mood (😢) - Very poor experience
  verySad,
}

/// Extension to provide additional functionality for MoodType
extension MoodTypeExtension on MoodType {
  /// Get the emoji representation of the mood
  String get emoji {
    switch (this) {
      case MoodType.veryHappy:
        return '😄';
      case MoodType.happy:
        return '😊';
      case MoodType.neutral:
        return '😐';
      case MoodType.sad:
        return '😔';
      case MoodType.verySad:
        return '😢';
    }
  }

  /// Get the display name of the mood
  String get displayName {
    switch (this) {
      case MoodType.veryHappy:
        return 'Very Happy';
      case MoodType.happy:
        return 'Happy';
      case MoodType.neutral:
        return 'Neutral';
      case MoodType.sad:
        return 'Sad';
      case MoodType.verySad:
        return 'Very Sad';
    }
  }

  /// Get the color associated with the mood
  Color get color {
    switch (this) {
      case MoodType.veryHappy:
        return const Color(0xFF4CAF50); // Green
      case MoodType.happy:
        return const Color(0xFF8BC34A); // Light Green
      case MoodType.neutral:
        return const Color(0xFF9E9E9E); // Grey
      case MoodType.sad:
        return const Color(0xFFFF9800); // Orange
      case MoodType.verySad:
        return const Color(0xFFF44336); // Red
    }
  }

  /// Get the numeric score for the mood (1-5 scale)
  int get score {
    switch (this) {
      case MoodType.veryHappy:
        return 5;
      case MoodType.happy:
        return 4;
      case MoodType.neutral:
        return 3;
      case MoodType.sad:
        return 2;
      case MoodType.verySad:
        return 1;
    }
  }

  /// Create MoodType from score (1-5)
  static MoodType fromScore(int score) {
    switch (score) {
      case 5:
        return MoodType.veryHappy;
      case 4:
        return MoodType.happy;
      case 3:
        return MoodType.neutral;
      case 2:
        return MoodType.sad;
      case 1:
      default:
        return MoodType.verySad;
    }
  }
}

/// Model representing a single mood entry
class MoodEntry {
  /// Unique identifier for the mood entry
  final String id;

  /// The mood type recorded
  final MoodType mood;

  /// Timestamp when the mood was recorded
  final DateTime timestamp;

  /// Optional experience ID if mood is related to an experience
  final String? experienceId;

  /// Optional booking ID if mood is related to a booking
  final String? bookingId;

  /// Optional notes provided by the user
  final String? notes;

  /// Additional context information
  final Map<String, dynamic> context;

  /// Creates a new mood entry
  const MoodEntry({
    required this.id,
    required this.mood,
    required this.timestamp,
    this.experienceId,
    this.bookingId,
    this.notes,
    this.context = const {},
  });

  /// Creates a new mood entry with generated ID
  factory MoodEntry.create({
    required MoodType mood,
    String? experienceId,
    String? bookingId,
    String? notes,
    Map<String, dynamic>? context,
  }) {
    return MoodEntry(
      id: const Uuid().v4(),
      mood: mood,
      timestamp: DateTime.now(),
      experienceId: experienceId,
      bookingId: bookingId,
      notes: notes,
      context: context ?? {},
    );
  }

  /// Creates a mood entry from JSON
  factory MoodEntry.fromJson(Map<String, dynamic> json) {
    return MoodEntry(
      id: json['id'] as String,
      mood: MoodType.values[json['mood'] as int],
      timestamp: DateTime.parse(json['timestamp'] as String),
      experienceId: json['experienceId'] as String?,
      bookingId: json['bookingId'] as String?,
      notes: json['notes'] as String?,
      context: Map<String, dynamic>.from(json['context'] as Map? ?? {}),
    );
  }

  /// Converts mood entry to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'mood': mood.index,
      'timestamp': timestamp.toIso8601String(),
      'experienceId': experienceId,
      'bookingId': bookingId,
      'notes': notes,
      'context': context,
    };
  }

  /// Creates a copy of the mood entry with updated values
  MoodEntry copyWith({
    String? id,
    MoodType? mood,
    DateTime? timestamp,
    String? experienceId,
    String? bookingId,
    String? notes,
    Map<String, dynamic>? context,
  }) {
    return MoodEntry(
      id: id ?? this.id,
      mood: mood ?? this.mood,
      timestamp: timestamp ?? this.timestamp,
      experienceId: experienceId ?? this.experienceId,
      bookingId: bookingId ?? this.bookingId,
      notes: notes ?? this.notes,
      context: context ?? this.context,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MoodEntry &&
        other.id == id &&
        other.mood == mood &&
        other.timestamp == timestamp &&
        other.experienceId == experienceId &&
        other.bookingId == bookingId &&
        other.notes == notes;
  }

  @override
  int get hashCode {
    return Object.hash(
      id,
      mood,
      timestamp,
      experienceId,
      bookingId,
      notes,
    );
  }

  @override
  String toString() {
    return 'MoodEntry(id: $id, mood: $mood, timestamp: $timestamp, experienceId: $experienceId, bookingId: $bookingId, notes: $notes)';
  }
}

/// Model representing mood trends over a period
class MoodTrend {
  /// Start date of the trend period
  final DateTime startDate;

  /// End date of the trend period
  final DateTime endDate;

  /// Distribution of moods in the period
  final Map<MoodType, int> moodDistribution;

  /// Average mood score for the period
  final double averageMoodScore;

  /// Improvement percentage compared to previous period
  final double improvementPercentage;

  /// List of mood entries in the period
  final List<MoodEntry> entries;

  /// Creates a new mood trend
  const MoodTrend({
    required this.startDate,
    required this.endDate,
    required this.moodDistribution,
    required this.averageMoodScore,
    required this.improvementPercentage,
    required this.entries,
  });

  /// Creates a mood trend from a list of mood entries
  factory MoodTrend.fromEntries({
    required DateTime startDate,
    required DateTime endDate,
    required List<MoodEntry> entries,
    double previousPeriodAverage = 0.0,
  }) {
    final moodDistribution = <MoodType, int>{};
    for (final moodType in MoodType.values) {
      moodDistribution[moodType] = 0;
    }

    double totalScore = 0.0;
    for (final entry in entries) {
      moodDistribution[entry.mood] = (moodDistribution[entry.mood] ?? 0) + 1;
      totalScore += entry.mood.score;
    }

    final averageScore = entries.isEmpty ? 0.0 : totalScore / entries.length;
    final improvement = previousPeriodAverage == 0.0
        ? 0.0
        : ((averageScore - previousPeriodAverage) / previousPeriodAverage) *
            100;

    return MoodTrend(
      startDate: startDate,
      endDate: endDate,
      moodDistribution: moodDistribution,
      averageMoodScore: averageScore,
      improvementPercentage: improvement,
      entries: entries,
    );
  }

  /// Get the most common mood in the period
  MoodType? get dominantMood {
    if (moodDistribution.isEmpty) return null;

    MoodType? dominant;
    int maxCount = 0;

    for (final entry in moodDistribution.entries) {
      if (entry.value > maxCount) {
        maxCount = entry.value;
        dominant = entry.key;
      }
    }

    return dominant;
  }

  /// Get the total number of mood entries
  int get totalEntries => entries.length;

  /// Check if the trend shows improvement
  bool get isImproving => improvementPercentage > 0;

  @override
  String toString() {
    return 'MoodTrend(startDate: $startDate, endDate: $endDate, averageScore: $averageMoodScore, improvement: $improvementPercentage%)';
  }
}

/// Model representing user preferences for mood tracking
class MoodPreferences {
  /// Whether mood tracking is enabled
  final bool trackingEnabled;

  /// Whether mood reminder notifications are enabled
  final bool notificationsEnabled;

  /// Frequency of mood reminder notifications
  final Duration reminderFrequency;

  /// Whether to share mood data with mascot for expressions
  final bool shareWithMascot;

  /// Whether to include mood data in analytics
  final bool includeInAnalytics;

  /// Data retention period in days
  final int dataRetentionDays;

  /// Creates new mood preferences
  const MoodPreferences({
    this.trackingEnabled = true,
    this.notificationsEnabled = true,
    this.reminderFrequency = const Duration(days: 1),
    this.shareWithMascot = true,
    this.includeInAnalytics = true,
    this.dataRetentionDays = 365,
  });

  /// Creates mood preferences from JSON
  factory MoodPreferences.fromJson(Map<String, dynamic> json) {
    return MoodPreferences(
      trackingEnabled: json['trackingEnabled'] as bool? ?? true,
      notificationsEnabled: json['notificationsEnabled'] as bool? ?? true,
      reminderFrequency:
          Duration(days: json['reminderFrequencyDays'] as int? ?? 1),
      shareWithMascot: json['shareWithMascot'] as bool? ?? true,
      includeInAnalytics: json['includeInAnalytics'] as bool? ?? true,
      dataRetentionDays: json['dataRetentionDays'] as int? ?? 365,
    );
  }

  /// Converts mood preferences to JSON
  Map<String, dynamic> toJson() {
    return {
      'trackingEnabled': trackingEnabled,
      'notificationsEnabled': notificationsEnabled,
      'reminderFrequencyDays': reminderFrequency.inDays,
      'shareWithMascot': shareWithMascot,
      'includeInAnalytics': includeInAnalytics,
      'dataRetentionDays': dataRetentionDays,
    };
  }

  /// Creates a copy with updated values
  MoodPreferences copyWith({
    bool? trackingEnabled,
    bool? notificationsEnabled,
    Duration? reminderFrequency,
    bool? shareWithMascot,
    bool? includeInAnalytics,
    int? dataRetentionDays,
  }) {
    return MoodPreferences(
      trackingEnabled: trackingEnabled ?? this.trackingEnabled,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      reminderFrequency: reminderFrequency ?? this.reminderFrequency,
      shareWithMascot: shareWithMascot ?? this.shareWithMascot,
      includeInAnalytics: includeInAnalytics ?? this.includeInAnalytics,
      dataRetentionDays: dataRetentionDays ?? this.dataRetentionDays,
    );
  }

  @override
  String toString() {
    return 'MoodPreferences(trackingEnabled: $trackingEnabled, notificationsEnabled: $notificationsEnabled, reminderFrequency: $reminderFrequency)';
  }
}
