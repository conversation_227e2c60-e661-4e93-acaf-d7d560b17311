/// A model representing a country
class Country {
  /// The country code (ISO 3166-1 alpha-2)
  final String code;

  /// The country name
  final String name;

  /// The country's dial code for phone numbers
  final String dialCode;

  /// The country's flag emoji
  final String flag;

  /// The country's currency code (ISO 4217)
  final String? currencyCode;

  /// The country's currency symbol
  final String? currencySymbol;

  /// The country's continent
  final String? continent;

  /// The country's capital city
  final String? capital;

  /// The country's languages (ISO 639-1 codes)
  final List<String> languages;

  /// Whether the country requires a visa for tourists
  final bool requiresVisa;

  /// Creates a new country
  const Country({
    required this.code,
    required this.name,
    required this.dialCode,
    required this.flag,
    this.currencyCode,
    this.currencySymbol,
    this.continent,
    this.capital,
    this.languages = const [],
    this.requiresVisa = false,
  });

  /// Get the formatted country name with flag
  String get displayName => '$flag $name';

  /// Get the formatted dial code
  String get formattedDialCode => '+$dialCode';

  /// Get the currency display name
  String get currencyDisplay {
    if (currencyCode != null && currencySymbol != null) {
      return '$currencySymbol ($currencyCode)';
    } else if (currencyCode != null) {
      return currencyCode!;
    } else if (currencySymbol != null) {
      return currencySymbol!;
    }
    return 'Unknown';
  }

  /// Get the primary language
  String? get primaryLanguage {
    return languages.isNotEmpty ? languages.first : null;
  }

  /// Check if the country uses a specific language
  bool hasLanguage(String languageCode) {
    return languages.contains(languageCode);
  }

  /// Create a country from JSON
  factory Country.fromJson(Map<String, dynamic> json) {
    return Country(
      code: json['code'] as String,
      name: json['name'] as String,
      dialCode: json['dialCode'] as String,
      flag: json['flag'] as String,
      currencyCode: json['currencyCode'] as String?,
      currencySymbol: json['currencySymbol'] as String?,
      continent: json['continent'] as String?,
      capital: json['capital'] as String?,
      languages: (json['languages'] as List<dynamic>?)
              ?.map((e) => e as String)
              .toList() ??
          [],
      requiresVisa: json['requiresVisa'] as bool? ?? false,
    );
  }

  /// Convert the country to JSON
  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'name': name,
      'dialCode': dialCode,
      'flag': flag,
      'currencyCode': currencyCode,
      'currencySymbol': currencySymbol,
      'continent': continent,
      'capital': capital,
      'languages': languages,
      'requiresVisa': requiresVisa,
    };
  }

  /// Create a copy of this country with the given fields replaced with new values
  Country copyWith({
    String? code,
    String? name,
    String? dialCode,
    String? flag,
    String? currencyCode,
    String? currencySymbol,
    String? continent,
    String? capital,
    List<String>? languages,
    bool? requiresVisa,
  }) {
    return Country(
      code: code ?? this.code,
      name: name ?? this.name,
      dialCode: dialCode ?? this.dialCode,
      flag: flag ?? this.flag,
      currencyCode: currencyCode ?? this.currencyCode,
      currencySymbol: currencySymbol ?? this.currencySymbol,
      continent: continent ?? this.continent,
      capital: capital ?? this.capital,
      languages: languages ?? this.languages,
      requiresVisa: requiresVisa ?? this.requiresVisa,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Country &&
        other.code == code &&
        other.name == name &&
        other.dialCode == dialCode &&
        other.flag == flag &&
        other.currencyCode == currencyCode &&
        other.currencySymbol == currencySymbol &&
        other.continent == continent &&
        other.capital == capital &&
        other.languages.length == languages.length &&
        other.languages.every((element) => languages.contains(element)) &&
        other.requiresVisa == requiresVisa;
  }

  @override
  int get hashCode {
    return Object.hash(
      code,
      name,
      dialCode,
      flag,
      currencyCode,
      currencySymbol,
      continent,
      capital,
      languages,
      requiresVisa,
    );
  }

  @override
  String toString() {
    return 'Country(code: $code, name: $name, dialCode: $dialCode, flag: $flag)';
  }
}

/// Common countries for quick access
class Countries {
  static const Country unitedStates = Country(
    code: 'US',
    name: 'United States',
    dialCode: '1',
    flag: '🇺🇸',
    currencyCode: 'USD',
    currencySymbol: '\$',
    continent: 'North America',
    capital: 'Washington, D.C.',
    languages: ['en'],
  );

  static const Country unitedKingdom = Country(
    code: 'GB',
    name: 'United Kingdom',
    dialCode: '44',
    flag: '🇬🇧',
    currencyCode: 'GBP',
    currencySymbol: '£',
    continent: 'Europe',
    capital: 'London',
    languages: ['en'],
  );

  static const Country canada = Country(
    code: 'CA',
    name: 'Canada',
    dialCode: '1',
    flag: '🇨🇦',
    currencyCode: 'CAD',
    currencySymbol: 'C\$',
    continent: 'North America',
    capital: 'Ottawa',
    languages: ['en', 'fr'],
  );

  static const Country australia = Country(
    code: 'AU',
    name: 'Australia',
    dialCode: '61',
    flag: '🇦🇺',
    currencyCode: 'AUD',
    currencySymbol: 'A\$',
    continent: 'Oceania',
    capital: 'Canberra',
    languages: ['en'],
  );

  static const Country germany = Country(
    code: 'DE',
    name: 'Germany',
    dialCode: '49',
    flag: '🇩🇪',
    currencyCode: 'EUR',
    currencySymbol: '€',
    continent: 'Europe',
    capital: 'Berlin',
    languages: ['de'],
  );

  static const Country france = Country(
    code: 'FR',
    name: 'France',
    dialCode: '33',
    flag: '🇫🇷',
    currencyCode: 'EUR',
    currencySymbol: '€',
    continent: 'Europe',
    capital: 'Paris',
    languages: ['fr'],
  );

  static const Country japan = Country(
    code: 'JP',
    name: 'Japan',
    dialCode: '81',
    flag: '🇯🇵',
    currencyCode: 'JPY',
    currencySymbol: '¥',
    continent: 'Asia',
    capital: 'Tokyo',
    languages: ['ja'],
    requiresVisa: true,
  );

  static const Country china = Country(
    code: 'CN',
    name: 'China',
    dialCode: '86',
    flag: '🇨🇳',
    currencyCode: 'CNY',
    currencySymbol: '¥',
    continent: 'Asia',
    capital: 'Beijing',
    languages: ['zh'],
    requiresVisa: true,
  );

  /// Get all common countries
  static List<Country> get all => [
        unitedStates,
        unitedKingdom,
        canada,
        australia,
        germany,
        france,
        japan,
        china,
      ];
}
