// Dart imports
import 'dart:math' as math;

// Flutter imports
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Project imports
import 'package:culture_connect/models/achievement/user_achievement.dart';
import 'package:culture_connect/theme/app_theme.dart';

/// Widget for displaying achievement celebration animations
class CelebrationAnimation extends StatefulWidget {
  /// The user achievement to celebrate
  final UserAchievement userAchievement;

  /// Callback when the celebration is complete
  final VoidCallback? onComplete;

  /// Whether to show the celebration immediately
  final bool autoStart;

  const CelebrationAnimation({
    super.key,
    required this.userAchievement,
    this.onComplete,
    this.autoStart = true,
  });

  @override
  State<CelebrationAnimation> createState() => _CelebrationAnimationState();
}

// Optimized: Single animation controller with consolidated animations
class _CelebrationAnimationState extends State<CelebrationAnimation>
    with SingleTickerProviderStateMixin {
  late AnimationController _masterController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _confettiAnimation;
  late Animation<double> _glowAnimation;

  bool _isAnimating = false;

  @override
  void initState() {
    super.initState();
    _initializeOptimizedAnimations();

    if (widget.autoStart) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _startCelebration();
      });
    }
  }

  // Optimized: Single controller with staggered intervals
  void _initializeOptimizedAnimations() {
    _masterController = AnimationController(
      duration: const Duration(milliseconds: 2000), // Total animation duration
      vsync: this,
    );

    // Optimized: Staggered animations using intervals
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _masterController,
      curve: const Interval(0.0, 0.2, curve: Curves.easeInOut),
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _masterController,
      curve: const Interval(0.1, 0.5, curve: Curves.elasticOut),
    ));

    _confettiAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _masterController,
      curve: const Interval(0.3, 0.9, curve: Curves.easeOut),
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _masterController,
      curve: const Interval(0.3, 0.7, curve: Curves.easeInOut),
    ));
  }

  // Optimized: Single controller animation start
  Future<void> _startCelebration() async {
    if (_isAnimating) return;

    setState(() {
      _isAnimating = true;
    });

    // Trigger haptic feedback
    await HapticFeedback.mediumImpact();

    // Start master animation (all sub-animations are staggered automatically)
    await _masterController.forward();

    if (mounted) {
      widget.onComplete?.call();
    }
  }

  // Optimized: Single controller disposal
  @override
  void dispose() {
    _masterController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final achievement = widget.userAchievement.achievement;

    return Material(
      color: Colors.black.withAlpha(128),
      child: Center(
        // Optimized: Single animation listener instead of Listenable.merge
        child: AnimatedBuilder(
          animation: _masterController,
          builder: (context, child) {
            return FadeTransition(
              opacity: _fadeAnimation,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  // Confetti particles
                  if (_confettiAnimation.value > 0)
                    ..._buildConfettiParticles(achievement.color),

                  // Main celebration card
                  ScaleTransition(
                    scale: _scaleAnimation,
                    child: Container(
                      width: 300,
                      padding: const EdgeInsets.all(24),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.surface,
                        borderRadius:
                            BorderRadius.circular(AppTheme.borderRadiusLarge),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(51),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Achievement unlocked text
                          Text(
                            'Achievement Unlocked!',
                            style: theme.textTheme.headlineSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: achievement.color,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 24),

                          // Achievement badge with glow effect
                          Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              boxShadow: _glowAnimation.value > 0
                                  ? [
                                      BoxShadow(
                                        color: achievement.color.withAlpha(
                                          (128 * _glowAnimation.value).round(),
                                        ),
                                        blurRadius: 30 * _glowAnimation.value,
                                        spreadRadius: 5 * _glowAnimation.value,
                                      ),
                                    ]
                                  : null,
                            ),
                            child: Container(
                              decoration: BoxDecoration(
                                color: achievement.color,
                                shape: BoxShape.circle,
                                gradient: RadialGradient(
                                  colors: [
                                    achievement.color,
                                    achievement.color.withAlpha(204),
                                  ],
                                ),
                              ),
                              child: Icon(
                                achievement.icon,
                                size: 60,
                                color: Colors.white,
                              ),
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Achievement title
                          Text(
                            achievement.title,
                            style: theme.textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 8),

                          // Achievement description
                          Text(
                            achievement.description,
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: theme.colorScheme.onSurfaceVariant,
                            ),
                            textAlign: TextAlign.center,
                          ),

                          const SizedBox(height: 24),

                          // Reward section
                          Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: achievement.color.withAlpha(26),
                              borderRadius: BorderRadius.circular(
                                  AppTheme.borderRadiusMedium),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.card_giftcard,
                                  color: achievement.color,
                                  size: 24,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Reward: ${achievement.rewardDisplayName}',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    color: achievement.color,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Continue button
                          SizedBox(
                            width: double.infinity,
                            child: ElevatedButton(
                              onPressed: () {
                                widget.onComplete?.call();
                              },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: achievement.color,
                                foregroundColor: Colors.white,
                                padding:
                                    const EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(
                                      AppTheme.borderRadiusMedium),
                                ),
                              ),
                              child: const Text(
                                'Continue',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }

  // Optimized: Reduced particle count and simplified calculations
  List<Widget> _buildConfettiParticles(Color primaryColor) {
    final particles = <Widget>[];
    const particleCount = 12; // Reduced from 20 for better performance

    // Optimized: Pre-calculate screen center once
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final centerX = screenWidth / 2;
    final centerY = screenHeight / 2;

    // Optimized: Pre-calculate animation values
    final animationValue = _confettiAnimation.value;
    final distance =
        120 * animationValue; // Reduced distance for better performance
    final opacity = (1 - animationValue).clamp(0.0, 1.0);

    // Optimized: Pre-defined colors array
    const colors = [Colors.amber, Colors.pink, Colors.blue];

    for (int i = 0; i < particleCount; i++) {
      final angle = (i / particleCount) * 6.28318; // 2 * pi pre-calculated
      final x = distance * math.cos(angle);
      final y = distance * math.sin(angle);

      particles.add(
        Positioned(
          left: centerX + x - 3, // Reduced particle size
          top: centerY + y - 3,
          child: Transform.rotate(
            angle: angle + (animationValue * 3), // Reduced rotation speed
            child: Opacity(
              opacity: opacity,
              child: Container(
                width: 6, // Reduced from 8
                height: 6,
                decoration: BoxDecoration(
                  color: i % 4 == 0 ? primaryColor : colors[i % 3],
                  shape: BoxShape.circle,
                ),
              ),
            ),
          ),
        ),
      );
    }

    return particles;
  }
}
