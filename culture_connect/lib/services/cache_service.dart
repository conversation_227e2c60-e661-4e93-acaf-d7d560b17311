import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:culture_connect/utils/lru_cache.dart';

/// Provider for the cache service
final cacheServiceProvider = Provider<CacheService>((ref) {
  return CacheService();
});

/// Service for caching data locally
class CacheService {
  // Singleton instance
  static final CacheService _instance = CacheService._internal();
  factory CacheService() => _instance;
  CacheService._internal();

  // Cache keys
  static const String _bookingsKey = 'cached_bookings';
  static const String _experiencesKey = 'cached_experiences';
  static const String _reviewsKey = 'cached_reviews';
  static const String _lastUpdatedPrefix = 'last_updated_';

  // Cache expiration time (24 hours)
  static const Duration _cacheExpiration = Duration(hours: 24);

  // Optimized: LRU cache for in-memory caching with size limits
  static const int _maxMemoryCacheSize = 100; // Max items in memory cache
  static const int _maxDiskCacheSize = 500; // Max items in disk cache

  final LRUCache<String, dynamic> _memoryCache =
      LRUCache(capacity: _maxMemoryCacheSize);
  final LRUCache<String, int> _accessTracker =
      LRUCache(capacity: _maxDiskCacheSize);

  // Cache statistics for monitoring
  int _cacheHits = 0;
  int _cacheMisses = 0;
  int _totalCacheSize = 0;

  // Optimized: Save data to cache with LRU memory caching and compression
  Future<bool> saveData(String key, dynamic data) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Store in memory cache for fast access
      _memoryCache.put(key, data);

      // Convert data to JSON string with compression
      final jsonString = jsonEncode(data);
      final compressedData = _compressData(jsonString);

      // Save compressed data to disk
      await prefs.setString(key, compressedData);

      // Save last updated timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      await prefs.setInt('$_lastUpdatedPrefix$key', timestamp);

      // Update access tracker and statistics
      _accessTracker.put(key, timestamp);
      _totalCacheSize += compressedData.length;

      return true;
    } catch (e) {
      debugPrint('Error saving data to cache: $e');
      return false;
    }
  }

  // Optimized: Get data from cache with LRU memory caching and decompression
  Future<dynamic> getData(String key, {bool checkExpiration = true}) async {
    try {
      // Check memory cache first (fastest)
      final memoryData = _memoryCache.get(key);
      if (memoryData != null) {
        _cacheHits++;
        return memoryData;
      }

      final prefs = await SharedPreferences.getInstance();

      // Check if data exists on disk
      if (!prefs.containsKey(key)) {
        _cacheMisses++;
        return null;
      }

      // Check if cache is expired
      if (checkExpiration) {
        final lastUpdated = prefs.getInt('$_lastUpdatedPrefix$key');
        if (lastUpdated != null) {
          final lastUpdatedTime =
              DateTime.fromMillisecondsSinceEpoch(lastUpdated);
          final now = DateTime.now();

          if (now.difference(lastUpdatedTime) > _cacheExpiration) {
            // Cache is expired, clear it
            await prefs.remove(key);
            await prefs.remove('$_lastUpdatedPrefix$key');
            _accessTracker.remove(key);
            _cacheMisses++;
            return null;
          }
        }
      }

      // Get compressed data from disk
      final compressedData = prefs.getString(key);
      if (compressedData == null) {
        _cacheMisses++;
        return null;
      }

      // Decompress and parse JSON
      final jsonString = _decompressData(compressedData);
      final data = jsonDecode(jsonString);

      // Store in memory cache for future access
      _memoryCache.put(key, data);

      // Update access tracker
      _accessTracker.put(key, DateTime.now().millisecondsSinceEpoch);

      _cacheHits++;
      return data;
    } catch (e) {
      debugPrint('Error getting data from cache: $e');
      _cacheMisses++;
      return null;
    }
  }

  /// Clear cache for a specific key
  Future<bool> clearCache(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Remove data and timestamp
      await prefs.remove(key);
      await prefs.remove('$_lastUpdatedPrefix$key');

      return true;
    } catch (e) {
      debugPrint('Error clearing cache: $e');
      return false;
    }
  }

  /// Clear all cache
  Future<bool> clearAllCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Clear all cache keys
      await prefs.remove(_bookingsKey);
      await prefs.remove(_experiencesKey);
      await prefs.remove(_reviewsKey);

      // Clear all timestamps
      await prefs.remove('$_lastUpdatedPrefix$_bookingsKey');
      await prefs.remove('$_lastUpdatedPrefix$_experiencesKey');
      await prefs.remove('$_lastUpdatedPrefix$_reviewsKey');

      return true;
    } catch (e) {
      debugPrint('Error clearing all cache: $e');
      return false;
    }
  }

  /// Check if cache is available and not expired
  Future<bool> isCacheAvailable(String key) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Check if data exists
      if (!prefs.containsKey(key)) {
        return false;
      }

      // Check if cache is expired
      final lastUpdated = prefs.getInt('$_lastUpdatedPrefix$key');
      if (lastUpdated == null) {
        return false;
      }

      final lastUpdatedTime = DateTime.fromMillisecondsSinceEpoch(lastUpdated);
      final now = DateTime.now();

      return now.difference(lastUpdatedTime) <= _cacheExpiration;
    } catch (e) {
      debugPrint('Error checking cache availability: $e');
      return false;
    }
  }

  /// Get bookings cache key
  String get bookingsKey => _bookingsKey;

  /// Get experiences cache key
  String get experiencesKey => _experiencesKey;

  /// Get reviews cache key
  String get reviewsKey => _reviewsKey;

  // Optimized: Simple compression for cache data
  String _compressData(String data) {
    try {
      // For now, use base64 encoding as simple compression
      // In production, consider using gzip compression
      final bytes = utf8.encode(data);
      final compressed = gzip.encode(bytes);
      return base64Encode(compressed);
    } catch (e) {
      debugPrint('Error compressing data: $e');
      return data; // Return original data if compression fails
    }
  }

  // Optimized: Simple decompression for cache data
  String _decompressData(String compressedData) {
    try {
      final bytes = base64Decode(compressedData);
      final decompressed = gzip.decode(bytes);
      return utf8.decode(decompressed);
    } catch (e) {
      debugPrint('Error decompressing data: $e');
      return compressedData; // Return original data if decompression fails
    }
  }

  // Optimized: Get cache statistics for monitoring
  Map<String, dynamic> getCacheStatistics() {
    return {
      'cache_hits': _cacheHits,
      'cache_misses': _cacheMisses,
      'hit_rate': _cacheHits + _cacheMisses > 0
          ? _cacheHits / (_cacheHits + _cacheMisses)
          : 0.0,
      'total_cache_size': _totalCacheSize,
      'memory_cache_size': _memoryCache.size,
      'disk_cache_tracked': _accessTracker.size,
    };
  }

  // Optimized: Intelligent cache cleanup based on LRU and usage patterns
  Future<void> performIntelligentCleanup() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final keys =
          prefs.getKeys().where((key) => !key.startsWith(_lastUpdatedPrefix));

      // Sort keys by last access time (LRU) from access tracker
      final sortedKeys = keys.toList()
        ..sort((a, b) {
          final aTime = _accessTracker.get(a) ?? 0;
          final bTime = _accessTracker.get(b) ?? 0;
          return aTime.compareTo(bTime); // Oldest first
        });

      // Remove oldest 20% of cache entries if we're over limit
      if (sortedKeys.length > _maxDiskCacheSize * 0.8) {
        final removeCount = (sortedKeys.length * 0.2).round();
        int removedSize = 0;

        for (int i = 0; i < removeCount && i < sortedKeys.length; i++) {
          final key = sortedKeys[i];
          final data = prefs.getString(key);
          if (data != null) {
            removedSize += data.length;
            await clearCache(key);
          }
        }

        _totalCacheSize -= removedSize;
        debugPrint(
            '🧹 Intelligent cleanup: removed $removeCount items, freed ${(removedSize / 1024).toStringAsFixed(1)}KB');
      }
    } catch (e) {
      debugPrint('❌ Error during intelligent cleanup: $e');
    }
  }
}
