import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'package:culture_connect/models/offline/content_priority.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Enhanced offline cache service with intelligent caching strategies
class EnhancedOfflineCacheService {
  static final EnhancedOfflineCacheService _instance =
      EnhancedOfflineCacheService._internal();
  factory EnhancedOfflineCacheService() => _instance;
  EnhancedOfflineCacheService._internal();

  final LoggingService _loggingService = LoggingService();

  late SharedPreferences _prefs;
  late Directory _cacheDirectory;

  // Cache configuration
  static const String _cacheMetadataKey = 'enhanced_cache_metadata';
  static const String _cacheStatsKey = 'cache_statistics';
  static const Duration _defaultCacheExpiry = Duration(days: 7);
  static const int _maxCacheSize = 500 * 1024 * 1024; // 500 MB

  // Cache metadata
  final Map<String, CacheMetadata> _cacheMetadata = {};
  final Map<String, CacheStatistics> _cacheStats = {};

  // Streams
  final StreamController<CacheEvent> _cacheEventController =
      StreamController<CacheEvent>.broadcast();

  /// Initialize the enhanced cache service
  Future<void> initialize() async {
    try {
      _prefs = await SharedPreferences.getInstance();
      _cacheDirectory = await _getCacheDirectory();

      await _loadCacheMetadata();
      await _loadCacheStatistics();
      await _performStartupCleanup();

      _loggingService.info(
          'EnhancedOfflineCacheService', 'Service initialized');
    } catch (e) {
      _loggingService.error(
          'EnhancedOfflineCacheService', 'Error initializing service: $e');
      rethrow;
    }
  }

  /// Get the cache directory
  Future<Directory> _getCacheDirectory() async {
    final appDir = await getApplicationDocumentsDirectory();
    final cacheDir = Directory('${appDir.path}/enhanced_cache');

    if (!await cacheDir.exists()) {
      await cacheDir.create(recursive: true);
    }

    return cacheDir;
  }

  /// Cache content with intelligent strategy
  Future<bool> cacheContent({
    required String contentId,
    required String contentType,
    required Map<String, dynamic> data,
    ContentPriority priority = ContentPriority.medium,
    Duration? customExpiry,
    List<String>? tags,
  }) async {
    try {
      final metadata = CacheMetadata(
        contentId: contentId,
        contentType: contentType,
        priority: priority,
        cachedAt: DateTime.now(),
        expiresAt: DateTime.now().add(customExpiry ?? _defaultCacheExpiry),
        tags: tags ?? [],
        accessCount: 0,
        lastAccessedAt: DateTime.now(),
        dataSize: _calculateDataSize(data),
      );

      // Check if we need to make space
      await _ensureCacheSpace(metadata.dataSize);

      // Save the data
      final success = await _saveCacheData(contentId, data);
      if (!success) return false;

      // Update metadata
      _cacheMetadata[contentId] = metadata;
      await _saveCacheMetadata();

      // Update statistics
      _updateCacheStatistics(
          contentType, CacheOperation.write, metadata.dataSize);

      // Emit cache event
      _cacheEventController.add(CacheEvent(
        type: CacheEventType.cached,
        contentId: contentId,
        contentType: contentType,
        timestamp: DateTime.now(),
      ));

      _loggingService.debug('EnhancedOfflineCacheService',
          'Cached content: $contentId (${metadata.dataSize} bytes)');

      return true;
    } catch (e) {
      _loggingService.error('EnhancedOfflineCacheService',
          'Error caching content $contentId: $e');
      return false;
    }
  }

  /// Retrieve cached content with access tracking
  Future<Map<String, dynamic>?> getCachedContent(String contentId) async {
    try {
      final metadata = _cacheMetadata[contentId];
      if (metadata == null) return null;

      // Check if expired
      if (DateTime.now().isAfter(metadata.expiresAt)) {
        await _removeCachedContent(contentId);
        return null;
      }

      // Load the data
      final data = await _loadCacheData(contentId);
      if (data == null) {
        // Metadata exists but data is missing - clean up
        await _removeCachedContent(contentId);
        return null;
      }

      // Update access tracking
      final updatedMetadata = metadata.copyWith(
        accessCount: metadata.accessCount + 1,
        lastAccessedAt: DateTime.now(),
      );
      _cacheMetadata[contentId] = updatedMetadata;
      await _saveCacheMetadata();

      // Update statistics
      _updateCacheStatistics(metadata.contentType, CacheOperation.read, 0);

      // Emit cache event
      _cacheEventController.add(CacheEvent(
        type: CacheEventType.accessed,
        contentId: contentId,
        contentType: metadata.contentType,
        timestamp: DateTime.now(),
      ));

      return data;
    } catch (e) {
      _loggingService.error('EnhancedOfflineCacheService',
          'Error getting cached content $contentId: $e');
      return null;
    }
  }

  /// Check if content is cached and valid
  bool isCached(String contentId) {
    final metadata = _cacheMetadata[contentId];
    if (metadata == null) return false;

    return DateTime.now().isBefore(metadata.expiresAt);
  }

  // Optimized: Intelligent cache preloading based on user behavior patterns
  Future<void> preloadFrequentContent() async {
    try {
      // Get frequently accessed content based on access count
      final frequentContent = _cacheMetadata.entries
          .where((entry) => entry.value.accessCount > 5)
          .toList()
        ..sort((a, b) => b.value.accessCount.compareTo(a.value.accessCount));

      // Preload top 20 most frequently accessed items
      final preloadTargets = frequentContent.take(20);

      for (final entry in preloadTargets) {
        final contentId = entry.key;
        final metadata = entry.value;

        // Skip if already cached or expired
        if (DateTime.now().isAfter(metadata.expiresAt)) continue;

        // Preload in background
        Future.microtask(() async {
          try {
            await getCachedContent(contentId);
            _loggingService.debug('EnhancedOfflineCacheService',
                'Preloaded frequent content: $contentId');
          } catch (e) {
            _loggingService.debug('EnhancedOfflineCacheService',
                'Failed to preload content: $contentId');
          }
        });
      }
    } catch (e) {
      _loggingService.error(
          'EnhancedOfflineCacheService', 'Error during content preloading: $e');
    }
  }

  // Optimized: Predictive content loading based on related content
  Future<void> preloadRelatedContent(
      String contentId, String contentType) async {
    try {
      // Find related content by type and tags
      final currentMetadata = _cacheMetadata[contentId];
      if (currentMetadata == null) return;

      final relatedContent = _cacheMetadata.entries
          .where((entry) =>
              entry.key != contentId &&
              entry.value.contentType == contentType &&
              entry.value.tags.any((tag) => currentMetadata.tags.contains(tag)))
          .take(5) // Limit to 5 related items
          .toList();

      // Preload related content in background
      for (final entry in relatedContent) {
        Future.microtask(() async {
          try {
            await getCachedContent(entry.key);
            _loggingService.debug('EnhancedOfflineCacheService',
                'Preloaded related content: ${entry.key}');
          } catch (e) {
            _loggingService.debug('EnhancedOfflineCacheService',
                'Failed to preload related content: ${entry.key}');
          }
        });
      }
    } catch (e) {
      _loggingService.error('EnhancedOfflineCacheService',
          'Error during related content preloading: $e');
    }
  }

  /// Get cache statistics
  Map<String, CacheStatistics> getCacheStatistics() {
    return Map.from(_cacheStats);
  }

  /// Get total cache size
  int getTotalCacheSize() {
    return _cacheMetadata.values
        .fold(0, (sum, metadata) => sum + metadata.dataSize);
  }

  /// Clean up expired content
  Future<int> cleanupExpiredContent() async {
    int cleanedCount = 0;
    final now = DateTime.now();

    final expiredIds = _cacheMetadata.entries
        .where((entry) => now.isAfter(entry.value.expiresAt))
        .map((entry) => entry.key)
        .toList();

    for (final contentId in expiredIds) {
      await _removeCachedContent(contentId);
      cleanedCount++;
    }

    if (cleanedCount > 0) {
      _loggingService.info('EnhancedOfflineCacheService',
          'Cleaned up $cleanedCount expired cache entries');
    }

    return cleanedCount;
  }

  /// Smart cleanup based on usage patterns
  Future<int> performSmartCleanup({int? targetSizeBytes}) async {
    final currentSize = getTotalCacheSize();
    final targetSize = targetSizeBytes ?? (_maxCacheSize * 0.8).round();

    if (currentSize <= targetSize) return 0;

    // Sort by cleanup priority (least recently used, lowest priority)
    final sortedEntries = _cacheMetadata.entries.toList()
      ..sort((a, b) {
        final aScore = _calculateCleanupScore(a.value);
        final bScore = _calculateCleanupScore(b.value);
        return aScore.compareTo(bScore);
      });

    int cleanedCount = 0;
    int freedSize = 0;

    for (final entry in sortedEntries) {
      if (currentSize - freedSize <= targetSize) break;

      freedSize += entry.value.dataSize;
      await _removeCachedContent(entry.key);
      cleanedCount++;
    }

    _loggingService.info('EnhancedOfflineCacheService',
        'Smart cleanup: removed $cleanedCount items, freed ${freedSize ~/ 1024}KB');

    return cleanedCount;
  }

  /// Calculate cleanup score (lower = more likely to be cleaned)
  double _calculateCleanupScore(CacheMetadata metadata) {
    final now = DateTime.now();
    final daysSinceAccess = now.difference(metadata.lastAccessedAt).inDays;
    final accessFrequency =
        metadata.accessCount / (now.difference(metadata.cachedAt).inDays + 1);

    // Lower score = higher cleanup priority
    return (metadata.priority.value * 10) +
        (accessFrequency * 5) -
        (daysSinceAccess * 0.5);
  }

  /// Stream of cache events
  Stream<CacheEvent> get cacheEventStream => _cacheEventController.stream;

  /// Dispose resources
  void dispose() {
    _cacheEventController.close();
  }

  // Private helper methods
  Future<void> _loadCacheMetadata() async {
    final metadataJson = _prefs.getString(_cacheMetadataKey);
    if (metadataJson != null) {
      final Map<String, dynamic> data = jsonDecode(metadataJson);
      data.forEach((key, value) {
        _cacheMetadata[key] = CacheMetadata.fromJson(value);
      });
    }
  }

  Future<void> _saveCacheMetadata() async {
    final data = <String, dynamic>{};
    _cacheMetadata.forEach((key, value) {
      data[key] = value.toJson();
    });
    await _prefs.setString(_cacheMetadataKey, jsonEncode(data));
  }

  Future<void> _loadCacheStatistics() async {
    final statsJson = _prefs.getString(_cacheStatsKey);
    if (statsJson != null) {
      final Map<String, dynamic> data = jsonDecode(statsJson);
      data.forEach((key, value) {
        _cacheStats[key] = CacheStatistics.fromJson(value);
      });
    }
  }

  Future<void> _saveCacheStatistics() async {
    final data = <String, dynamic>{};
    _cacheStats.forEach((key, value) {
      data[key] = value.toJson();
    });
    await _prefs.setString(_cacheStatsKey, jsonEncode(data));
  }

  Future<void> _performStartupCleanup() async {
    await cleanupExpiredContent();

    // Verify cache integrity
    final metadataIds = _cacheMetadata.keys.toSet();
    final fileIds = <String>{};

    await for (final entity in _cacheDirectory.list()) {
      if (entity is File && entity.path.endsWith('.cache')) {
        final filename = entity.path.split('/').last;
        final contentId = filename.replaceAll('.cache', '');
        fileIds.add(contentId);
      }
    }

    // Remove orphaned metadata
    final orphanedMetadata = metadataIds.difference(fileIds);
    for (final contentId in orphanedMetadata) {
      _cacheMetadata.remove(contentId);
    }

    // Remove orphaned files
    final orphanedFiles = fileIds.difference(metadataIds);
    for (final contentId in orphanedFiles) {
      final file = File('${_cacheDirectory.path}/$contentId.cache');
      if (await file.exists()) {
        await file.delete();
      }
    }

    if (orphanedMetadata.isNotEmpty || orphanedFiles.isNotEmpty) {
      await _saveCacheMetadata();
      _loggingService.info('EnhancedOfflineCacheService',
          'Cleaned up ${orphanedMetadata.length} orphaned metadata entries and ${orphanedFiles.length} orphaned files');
    }
  }

  Future<bool> _saveCacheData(
      String contentId, Map<String, dynamic> data) async {
    try {
      final file = File('${_cacheDirectory.path}/$contentId.cache');
      await file.writeAsString(jsonEncode(data));
      return true;
    } catch (e) {
      _loggingService.error('EnhancedOfflineCacheService',
          'Error saving cache data for $contentId: $e');
      return false;
    }
  }

  Future<Map<String, dynamic>?> _loadCacheData(String contentId) async {
    try {
      final file = File('${_cacheDirectory.path}/$contentId.cache');
      if (!await file.exists()) return null;

      final content = await file.readAsString();
      return jsonDecode(content) as Map<String, dynamic>;
    } catch (e) {
      _loggingService.error('EnhancedOfflineCacheService',
          'Error loading cache data for $contentId: $e');
      return null;
    }
  }

  Future<void> _removeCachedContent(String contentId) async {
    try {
      final file = File('${_cacheDirectory.path}/$contentId.cache');
      if (await file.exists()) {
        await file.delete();
      }

      _cacheMetadata.remove(contentId);
      await _saveCacheMetadata();

      _cacheEventController.add(CacheEvent(
        type: CacheEventType.removed,
        contentId: contentId,
        contentType: '',
        timestamp: DateTime.now(),
      ));
    } catch (e) {
      _loggingService.error('EnhancedOfflineCacheService',
          'Error removing cached content $contentId: $e');
    }
  }

  Future<void> _ensureCacheSpace(int requiredSize) async {
    final currentSize = getTotalCacheSize();
    if (currentSize + requiredSize > _maxCacheSize) {
      await performSmartCleanup(
        targetSizeBytes: _maxCacheSize - requiredSize,
      );
    }
  }

  int _calculateDataSize(Map<String, dynamic> data) {
    return jsonEncode(data).length;
  }

  void _updateCacheStatistics(
      String contentType, CacheOperation operation, int bytes) {
    final stats = _cacheStats[contentType] ??
        CacheStatistics(
          contentType: contentType,
          totalReads: 0,
          totalWrites: 0,
          totalBytes: 0,
          lastUpdated: DateTime.now(),
        );

    final updatedStats = stats.copyWith(
      totalReads: operation == CacheOperation.read
          ? stats.totalReads + 1
          : stats.totalReads,
      totalWrites: operation == CacheOperation.write
          ? stats.totalWrites + 1
          : stats.totalWrites,
      totalBytes: operation == CacheOperation.write
          ? stats.totalBytes + bytes
          : stats.totalBytes,
      lastUpdated: DateTime.now(),
    );

    _cacheStats[contentType] = updatedStats;
    _saveCacheStatistics();
  }
}

/// Cache metadata for tracking cached content
class CacheMetadata {
  final String contentId;
  final String contentType;
  final ContentPriority priority;
  final DateTime cachedAt;
  final DateTime expiresAt;
  final List<String> tags;
  final int accessCount;
  final DateTime lastAccessedAt;
  final int dataSize;

  const CacheMetadata({
    required this.contentId,
    required this.contentType,
    required this.priority,
    required this.cachedAt,
    required this.expiresAt,
    required this.tags,
    required this.accessCount,
    required this.lastAccessedAt,
    required this.dataSize,
  });

  CacheMetadata copyWith({
    String? contentId,
    String? contentType,
    ContentPriority? priority,
    DateTime? cachedAt,
    DateTime? expiresAt,
    List<String>? tags,
    int? accessCount,
    DateTime? lastAccessedAt,
    int? dataSize,
  }) {
    return CacheMetadata(
      contentId: contentId ?? this.contentId,
      contentType: contentType ?? this.contentType,
      priority: priority ?? this.priority,
      cachedAt: cachedAt ?? this.cachedAt,
      expiresAt: expiresAt ?? this.expiresAt,
      tags: tags ?? this.tags,
      accessCount: accessCount ?? this.accessCount,
      lastAccessedAt: lastAccessedAt ?? this.lastAccessedAt,
      dataSize: dataSize ?? this.dataSize,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'contentId': contentId,
      'contentType': contentType,
      'priority': priority.index,
      'cachedAt': cachedAt.toIso8601String(),
      'expiresAt': expiresAt.toIso8601String(),
      'tags': tags,
      'accessCount': accessCount,
      'lastAccessedAt': lastAccessedAt.toIso8601String(),
      'dataSize': dataSize,
    };
  }

  factory CacheMetadata.fromJson(Map<String, dynamic> json) {
    return CacheMetadata(
      contentId: json['contentId'] as String,
      contentType: json['contentType'] as String,
      priority: ContentPriority.values[json['priority'] as int],
      cachedAt: DateTime.parse(json['cachedAt'] as String),
      expiresAt: DateTime.parse(json['expiresAt'] as String),
      tags: List<String>.from(json['tags'] as List),
      accessCount: json['accessCount'] as int,
      lastAccessedAt: DateTime.parse(json['lastAccessedAt'] as String),
      dataSize: json['dataSize'] as int,
    );
  }
}

/// Cache statistics for monitoring
class CacheStatistics {
  final String contentType;
  final int totalReads;
  final int totalWrites;
  final int totalBytes;
  final DateTime lastUpdated;

  const CacheStatistics({
    required this.contentType,
    required this.totalReads,
    required this.totalWrites,
    required this.totalBytes,
    required this.lastUpdated,
  });

  CacheStatistics copyWith({
    String? contentType,
    int? totalReads,
    int? totalWrites,
    int? totalBytes,
    DateTime? lastUpdated,
  }) {
    return CacheStatistics(
      contentType: contentType ?? this.contentType,
      totalReads: totalReads ?? this.totalReads,
      totalWrites: totalWrites ?? this.totalWrites,
      totalBytes: totalBytes ?? this.totalBytes,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'contentType': contentType,
      'totalReads': totalReads,
      'totalWrites': totalWrites,
      'totalBytes': totalBytes,
      'lastUpdated': lastUpdated.toIso8601String(),
    };
  }

  factory CacheStatistics.fromJson(Map<String, dynamic> json) {
    return CacheStatistics(
      contentType: json['contentType'] as String,
      totalReads: json['totalReads'] as int,
      totalWrites: json['totalWrites'] as int,
      totalBytes: json['totalBytes'] as int,
      lastUpdated: DateTime.parse(json['lastUpdated'] as String),
    );
  }
}

/// Cache event for monitoring
class CacheEvent {
  final CacheEventType type;
  final String contentId;
  final String contentType;
  final DateTime timestamp;

  const CacheEvent({
    required this.type,
    required this.contentId,
    required this.contentType,
    required this.timestamp,
  });
}

/// Cache event types
enum CacheEventType {
  cached,
  accessed,
  removed,
  expired,
}

/// Cache operations
enum CacheOperation {
  read,
  write,
}
