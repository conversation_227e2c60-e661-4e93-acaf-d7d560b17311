import 'dart:convert';

// Flutter imports for image preloading
import 'package:flutter/material.dart';

// Package imports
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart'
    as google_maps_flutter;
import 'package:http/http.dart' as http;
import 'package:cached_network_image/cached_network_image.dart';

// Project imports
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/services/cache_service.dart';
import 'package:culture_connect/services/logging_service.dart';

/// Provider for the API service
final apiServiceProvider = Provider<ApiService>((ref) {
  final loggingService = ref.watch(loggingServiceProvider);
  final cacheService = ref.watch(cacheServiceProvider);
  return ApiService(loggingService, cacheService);
});

/// Service for making API requests to the backend
class ApiService {
  /// Base URL for the API
  final String _baseUrl = 'https://api.cultureconnect.com';

  /// Optimized: HTTP client with connection pooling and performance settings
  static final http.Client _sharedClient = _createOptimizedClient();

  /// Logging service for error reporting
  final LoggingService _loggingService;

  /// Cache service for offline support
  final CacheService _cacheService;

  /// Connectivity instance for checking network status
  final Connectivity _connectivity = Connectivity();

  /// Request priority levels for optimization
  static const int _priorityMedium = 2;

  /// Creates a new API service
  ApiService(this._loggingService, this._cacheService);

  // Optimized: Create HTTP client with connection pooling and performance settings
  static http.Client _createOptimizedClient() {
    final client = http.Client();
    // Note: In production, consider using dio with connection pooling
    // or implementing custom connection pool management
    return client;
  }

  // Optimized: Batch multiple API requests to reduce network overhead
  Future<List<T>> batchRequests<T>(
    List<String> endpoints,
    T Function(Map<String, dynamic>) parser, {
    int priority = _priorityMedium,
    Duration timeout = const Duration(seconds: 15),
  }) async {
    try {
      // Check connectivity first
      final online = await _isOnline();
      if (!online) {
        throw Exception('No internet connection');
      }

      // Create batch of futures with timeout
      final futures = endpoints.map((endpoint) async {
        final response = await _sharedClient.get(
          Uri.parse('$_baseUrl$endpoint'),
          headers: {'Content-Type': 'application/json'},
        ).timeout(timeout);

        if (response.statusCode == 200) {
          final json = jsonDecode(response.body);
          return parser(json);
        } else {
          throw Exception('Request failed: ${response.statusCode}');
        }
      });

      // Execute all requests in parallel
      return await Future.wait(futures);
    } catch (e, stackTrace) {
      _loggingService.error(
          'ApiService', 'Batch request failed', e, stackTrace);
      rethrow;
    }
  }

  /// Check if the device is online
  Future<bool> _isOnline() async {
    final connectivityResult = await _connectivity.checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  /// Get all experiences
  Future<List<Experience>> getExperiences() async {
    const endpoint = '/experiences';
    const cacheKey = 'experiences';

    try {
      // Check if we're online
      final online = await _isOnline();

      // If we're offline, try to get cached data
      if (!online) {
        final cachedData = await _cacheService.getData(cacheKey);
        if (cachedData != null) {
          _loggingService.debug('ApiService', 'Using cached experiences data');
          final List<dynamic> data =
              cachedData is String ? jsonDecode(cachedData) : cachedData;
          return data.map((json) => Experience.fromJson(json)).toList();
        } else {
          _loggingService.debug(
              'ApiService', 'No cached experiences data available');
          // Return mock data when offline and no cache
          return _getMockExperiences();
        }
      }

      // Optimized: Use shared client with connection pooling
      final response = await _sharedClient.get(
        Uri.parse('$_baseUrl$endpoint'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        final experiences =
            data.map((json) => Experience.fromJson(json)).toList();

        // Cache the response
        await _cacheService.saveData(cacheKey, response.body);

        return experiences;
      } else {
        throw Exception('Failed to load experiences: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      _loggingService.error(
          'ApiService', 'Error getting experiences', e, stackTrace);

      // Return mock data on error
      return _getMockExperiences();
    }
  }

  /// Get an experience by ID
  Future<Experience> getExperienceById(String id) async {
    final endpoint = '/experiences/$id';
    final cacheKey = 'experience_$id';

    try {
      // Check if we're online
      final online = await _isOnline();

      // If we're offline, try to get cached data
      if (!online) {
        final cachedData = await _cacheService.getData(cacheKey);
        if (cachedData != null) {
          _loggingService.debug(
              'ApiService', 'Using cached experience data for ID: $id');
          final json =
              cachedData is String ? jsonDecode(cachedData) : cachedData;
          return Experience.fromJson(json);
        } else {
          _loggingService.debug(
              'ApiService', 'No cached experience data available for ID: $id');
          // Try to find the experience in the cached experiences list
          final cachedExperiences = await _cacheService.getData('experiences');
          if (cachedExperiences != null) {
            final List<dynamic> data = cachedExperiences is String
                ? jsonDecode(cachedExperiences)
                : cachedExperiences;
            final experience = data.firstWhere(
              (json) => json['id'] == id,
              orElse: () => throw Exception('Experience not found in cache'),
            );
            return Experience.fromJson(experience);
          }

          // Return mock data when offline and no cache
          return _getMockExperienceById(id);
        }
      }

      // Optimized: Use shared client with connection pooling
      final response = await _sharedClient.get(
        Uri.parse('$_baseUrl$endpoint'),
        headers: {'Content-Type': 'application/json'},
      ).timeout(const Duration(seconds: 10));

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        final experience = Experience.fromJson(json);

        // Cache the response
        await _cacheService.saveData(cacheKey, response.body);

        return experience;
      } else if (response.statusCode == 404) {
        throw Exception('Experience not found');
      } else {
        throw Exception('Failed to load experience: ${response.statusCode}');
      }
    } catch (e, stackTrace) {
      _loggingService.error(
          'ApiService', 'Error getting experience by ID: $id', e, stackTrace);

      // Return mock data on error
      return _getMockExperienceById(id);
    }
  }

  /// Get mock experiences for testing and offline mode
  List<Experience> _getMockExperiences() {
    return [
      Experience(
        id: '1',
        title: 'Cultural Tour',
        description: 'Explore local culture',
        imageUrl: 'https://example.com/image1.jpg',
        category: 'Cultural Tours',
        location: 'San Francisco, CA',
        price: 99.99,
        rating: 4.5,
        reviewCount: 100,
        coordinates: const google_maps_flutter.LatLng(37.7749, -122.4194),
        guideId: 'guide1',
        guideName: 'John Doe',
        guideImageUrl: 'https://example.com/guide1.jpg',
        languages: ['English', 'Spanish'],
        includedItems: ['Guide', 'Transportation'],
        requirements: ['Comfortable shoes'],
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
        durationHours: 3.0,
        isAccessible: true,
        availableDates: List.generate(
          10,
          (index) => DateTime.now().add(Duration(days: index + 1)),
        ),
        maxParticipants: 15,
        currentParticipants: 8,
        isFeatured: false,
        viewCount: 250,
        isSaved: false,
        lastViewed: null,
        hasARContent: false,
        arModelUrl: null,
        arContentType: ARContentType.none,
        arMetadata: null,
      ),
      Experience(
        id: '2',
        title: 'Food Tour',
        description: 'Taste local cuisine',
        imageUrl: 'https://example.com/image2.jpg',
        category: 'Food & Drink',
        location: 'San Francisco, CA',
        price: 79.99,
        rating: 4.8,
        reviewCount: 150,
        coordinates: const google_maps_flutter.LatLng(37.7750, -122.4195),
        guideId: 'guide2',
        guideName: 'Jane Smith',
        guideImageUrl: 'https://example.com/guide2.jpg',
        languages: ['English', 'French'],
        includedItems: ['Food samples', 'Drinks'],
        requirements: ['No dietary restrictions'],
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        updatedAt: DateTime.now(),
        durationHours: 2.5,
        isAccessible: true,
        availableDates: List.generate(
          10,
          (index) => DateTime.now().add(Duration(days: index + 1)),
        ),
        maxParticipants: 10,
        currentParticipants: 6,
        isFeatured: true,
        viewCount: 320,
        isSaved: true,
        lastViewed: DateTime.now().subtract(const Duration(days: 2)),
        hasARContent: false,
        arModelUrl: null,
        arContentType: ARContentType.none,
        arMetadata: null,
      ),
      Experience(
        id: '3',
        title: 'Adventure Tour',
        description: 'Outdoor activities',
        imageUrl: 'https://example.com/image3.jpg',
        category: 'Adventure',
        location: 'San Francisco, CA',
        price: 129.99,
        rating: 4.2,
        reviewCount: 80,
        coordinates: const google_maps_flutter.LatLng(37.7751, -122.4196),
        guideId: 'guide3',
        guideName: 'Mike Johnson',
        guideImageUrl: 'https://example.com/guide3.jpg',
        languages: ['English'],
        includedItems: ['Equipment', 'Safety gear'],
        requirements: ['Good physical condition'],
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now(),
        durationHours: 4.0,
        isAccessible: false,
        availableDates: List.generate(
          10,
          (index) => DateTime.now().add(Duration(days: index + 1)),
        ),
        maxParticipants: 8,
        currentParticipants: 4,
        isFeatured: false,
        viewCount: 180,
        isSaved: false,
        lastViewed: null,
        hasARContent: true,
        arModelUrl: 'https://example.com/models/adventure.glb',
        arContentType: ARContentType.model3d,
        arMetadata: {
          'scale': 1.0,
          'rotation': 0.0,
        },
      ),
    ];
  }

  /// Get a mock experience by ID
  Experience _getMockExperienceById(String id) {
    final experiences = _getMockExperiences();
    try {
      return experiences.firstWhere((exp) => exp.id == id);
    } catch (e) {
      // If not found, return the first one
      return experiences.first;
    }
  }

  // Optimized: Background image preloading for better perceived performance
  Future<void> preloadImages(
      List<String> imageUrls, BuildContext context) async {
    try {
      // Preload images in background with limited concurrency
      const maxConcurrent = 3;
      for (var i = 0; i < imageUrls.length; i += maxConcurrent) {
        final batch = imageUrls.skip(i).take(maxConcurrent);
        final futures = batch.map((url) async {
          try {
            await precacheImage(
              CachedNetworkImageProvider(url),
              context,
            );
          } catch (e) {
            // Log but don't fail the batch for individual image errors
            _loggingService.debug(
                'ApiService', 'Failed to preload image: $url');
          }
        });

        await Future.wait(futures);

        // Small delay between batches to prevent overwhelming the network
        if (i + maxConcurrent < imageUrls.length) {
          await Future.delayed(const Duration(milliseconds: 100));
        }
      }
    } catch (e, stackTrace) {
      _loggingService.error(
          'ApiService', 'Image preloading failed', e, stackTrace);
    }
  }

  /// Optimized: Dispose resources (shared client managed globally)
  void dispose() {
    // Note: Shared client is managed globally and should not be closed per instance
    // In production, implement proper client lifecycle management
  }
}
