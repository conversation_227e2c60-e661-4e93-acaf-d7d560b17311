import 'dart:async';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:culture_connect/services/ar_service.dart';
import 'package:culture_connect/services/location_service.dart';
import 'package:culture_connect/models/experience.dart';
import 'package:culture_connect/widgets/experience_card.dart';

class ARViewScreen extends StatefulWidget {
  const ARViewScreen({super.key});

  @override
  State<ARViewScreen> createState() => _ARViewScreenState();
}

class _ARViewScreenState extends State<ARViewScreen> {
  late ARService _arService;
  bool _isInitialized = false;
  Experience? _currentExperience;
  bool _isLoading = true;

  // Optimized: Timer for AR detection instead of recursive Future.delayed
  Timer? _detectionTimer;

  @override
  void initState() {
    super.initState();
    _initializeAR();
  }

  Future<void> _initializeAR() async {
    try {
      _arService = ARService(LocationService());
      await _arService.initialize();
      await _arService.startAR();
      setState(() {
        _isInitialized = true;
        _isLoading = false;
      });
      _startExperienceDetection();
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error initializing AR: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  // Optimized: Timer-based detection instead of recursive Future.delayed
  void _startExperienceDetection() {
    // Cancel existing timer if any
    _detectionTimer?.cancel();

    // Start periodic detection with proper timer
    _detectionTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      _detectExperience();
    });
  }

  Future<void> _detectExperience() async {
    if (!_isInitialized) return;

    final experience = await _arService.detectExperienceInView();
    if (experience != _currentExperience) {
      setState(() {
        _currentExperience = experience;
      });
    }
  }

  // Optimized: Proper cleanup of timer and AR resources
  @override
  void dispose() {
    _detectionTimer?.cancel();
    _arService.stopAR();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (!_isInitialized) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(
                Icons.error_outline,
                size: 48,
                color: Colors.red,
              ),
              const SizedBox(height: 16),
              Text(
                'Failed to initialize AR',
                style: theme.textTheme.titleLarge,
              ),
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: _initializeAR,
                child: const Text('Retry'),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      body: Stack(
        children: [
          // Camera Preview
          if (_arService.cameraController != null)
            SizedBox.expand(
              child: FittedBox(
                fit: BoxFit.cover,
                child: SizedBox(
                  width: _arService.cameraController!.value.previewSize?.width,
                  height:
                      _arService.cameraController!.value.previewSize?.height,
                  child: CameraPreview(_arService.cameraController!),
                ),
              ),
            ),

          // AR Overlay
          if (_currentExperience != null)
            Positioned(
              bottom: 16,
              left: 16,
              right: 16,
              child: ExperienceCard(
                title: _currentExperience!.title,
                imageUrl: _currentExperience!.imageUrl,
                rating: _currentExperience!.rating,
                reviewCount: _currentExperience!.reviewCount,
                price: _currentExperience!.price.toString(),
                category: _currentExperience!.category,
                location: _currentExperience!.location,
                onTap: () {
                  // Navigate to experience details
                  Navigator.pushNamed(
                    context,
                    '/experience_details',
                    arguments: _currentExperience!.id,
                  );
                },
              ),
            ),

          // Close Button
          Positioned(
            top: 16,
            right: 16,
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surface.withAlpha(204), // 0.8 opacity
                shape: BoxShape.circle,
              ),
              child: IconButton(
                icon: const Icon(Icons.close),
                onPressed: () => Navigator.pop(context),
              ),
            ),
          ),

          // Instructions
          Positioned(
            top: 16,
            left: 16,
            right: 80,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              decoration: BoxDecoration(
                color: theme.colorScheme.surface.withAlpha(204), // 0.8 opacity
                borderRadius: BorderRadius.circular(20),
              ),
              child: Text(
                'Point your camera at a location to see information',
                style: theme.textTheme.bodyMedium,
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
